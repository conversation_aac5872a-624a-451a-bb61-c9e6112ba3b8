@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme {
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --font-sans: Inter, Inter override, ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }

    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }

    to {
      height: 0;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;

  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }

  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}
@layer base {
  :root {
    --background: 228 56% 99%; /* off-white with a hint of blue */
    --foreground: 232 60% 14%; /* deep blue for text */
    --muted: 228 40% 94%; /* very light blue for muted backgrounds */
    --muted-foreground: 232 30% 40%; /* muted blue for text */
    --popover: 0 0% 100%;
    --popover-foreground: 232 60% 14%;
    --card: 0 0% 100%;
    --card-foreground: 232 60% 14%;
    --border: 228 24% 85%; /* subtle blue border */
    --input: 228 24% 85%;
    --primary: 234 80% 38%; /* deep, rich blue #2336a3 */
    --primary-foreground: 0 0% 100%;
    --secondary: 245 33% 60%; /* soft, academic purple */
    --secondary-foreground: 0 0% 100%;
    --accent: 48 97% 60%; /* soft gold #ffe066 */
    --accent-foreground: 232 60% 14%;
    --destructive: 2 80% 55%;
    --destructive-foreground: 0 0% 100%;
    --ring: 234 80% 38%;
    --chart-1: 234 80% 38%;
    --chart-2: 245 33% 60%;
    --chart-3: 48 97% 60%;
    --chart-4: 245 33% 65%;
    --chart-5: 234 90% 38%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 230 25% 10%; /* deep, nearly-black navy */
    --foreground: 48 20% 92%; /* soft off-white/gold for text */
    --muted: 230 15% 18%; /* dark muted blue-gray */
    --muted-foreground: 48 20% 70%; /* muted gold/white for muted text */
    --popover: 230 25% 12%; /* slightly lighter than background */
    --popover-foreground: 48 20% 92%;
    --card: 230 20% 13%; /* dark card background */
    --card-foreground: 48 20% 92%;
    --border: 230 15% 22%; /* subtle border */
    --input: 230 15% 22%;
    --primary: 234 80% 60%; /* blue accent, but less saturated in dark */
    --primary-foreground: 0 0% 100%;
    --secondary: 245 20% 40%; /* muted purple for secondary */
    --secondary-foreground: 0 0% 100%;
    --accent: 48 97% 60%; /* gold accent */
    --accent-foreground: 230 25% 10%;
    --destructive: 2 80% 65%;
    --destructive-foreground: 0 0% 100%;
    --ring: 234 80% 60%;
    --chart-1: 234 80% 60%;
    --chart-2: 245 20% 40%;
    --chart-3: 48 97% 60%;
    --chart-4: 245 20% 45%;
    --chart-5: 234 90% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-background text-foreground;
    /* font-feature-settings: "rlig" 1, "calt" 1; */
    font-synthesis-weight: none;
    text-rendering: optimizeLegibility;
  }
}

/*
  ---break---
*/

:root {
  --sidebar: hsl(0 0% 98%);
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

/*
  ---break---
*/

.dark {
  --sidebar: hsl(240 5.9% 10%);
  --sidebar-foreground: hsl(240 4.8% 95.9%);
  --sidebar-primary: hsl(224.3 76.3% 48%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(240 3.7% 15.9%);
  --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

/*
  ---break---
*/

@theme inline {
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  /*
  ---break---
*/
  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  /*
  ---break---
*/
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

/*
  ---break---
*/

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
