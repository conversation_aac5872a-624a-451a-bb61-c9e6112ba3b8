---
import HeadSE<PERSON> from "@/components/HeadSEO.astro";
import Footer from "@/components/Footer.astro";
import Header from "@/components/Header.astro";

interface Props {
  title?: string | undefined;
  description?: string | undefined;
  ogImage?: URL | undefined;
}

const { title, description, ogImage } = Astro.props;
---

<html lang="en">
  <head>
    <HeadSEO title={title} description={description} ogImage={ogImage} />
  </head>
  <body class="bg-background min-h-screen font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
      <Header />
      <main class="flex-auto">
        <slot />
      </main>
      <Footer />
    </div>

    <!-- Theme script to ensure light mode is default -->
    <script is:inline>
      const getThemePreference = () => {
        if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
          return localStorage.getItem('theme')
        }
        return 'light' // Default to light theme instead of using system preference
      }
      const isDark = getThemePreference() === 'dark'
      document.documentElement.classList[isDark ? 'add' : 'remove']('dark')

      if (typeof localStorage !== 'undefined') {
        const observer = new MutationObserver(() => {
          const isDark = document.documentElement.classList.contains('dark')
          localStorage.setItem('theme', isDark ? 'dark' : 'light')
        })
        observer.observe(document.documentElement, {
          attributes: true,
          attributeFilter: ['class'],
        })
      }
    </script>
  </body>
</html>
