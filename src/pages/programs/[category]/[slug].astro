---
import CollegeLayout from '@/layouts/CollegeLayout.astro';
import { programs } from '@/lib/data/programs';

export async function getStaticPaths() {
  return programs.map(program => {
    const [, , category, slug] = program.learnMoreLink.split('/');
    return {
      params: { category, slug },
      props: { program },
    };
  });
}

const { program } = Astro.props;

const getCategoryLink = (category: string) => {
  switch (category) {
    case 'Undergraduate':
      return '/academics/undergraduate';
    case 'Graduate':
      return '/academics/graduate';
    case 'TESDA':
      return '/academics/tesda-courses';
    default:
      return '/academics'; 
  }
};
const backLink = getCategoryLink(program.category);
---

<CollegeLayout title={program.title}>
  <div class="container mx-auto px-4 py-12 sm:py-16 lg:py-20">
    <div class="max-w-4xl mx-auto">
      <a href={backLink} class="text-primary hover:underline mb-6 inline-block">&larr; Back to {program.category} Programs</a>

      <div class="relative mb-8">
        <img src={program.image} alt={`Image for ${program.title}`} class="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg" />
        <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent rounded-lg"></div>
        <div class="absolute bottom-8 left-8">
          <p class="text-lg font-semibold text-white/80">{program.degree}</p>
          <h1 class="text-4xl md:text-5xl font-bold text-white tracking-tight">{program.title}</h1>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="md:col-span-2">
          <h2 class="text-2xl font-bold mb-4">Program Overview</h2>
          <p class="text-muted-foreground text-lg">{program.description}</p>
          
          <h3 class="text-xl font-bold mt-8 mb-4">Program Details</h3>
          <div class="grid grid-cols-2 gap-4 text-muted-foreground">
            <div class="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 text-primary"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
              <div>
                <strong>Duration:</strong> {program.duration}
              </div>
            </div>
            <div class="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 text-primary"><map-pin/></svg>
              <div>
                <strong>Format:</strong> {program.format}
              </div>
            </div>
          </div>
        </div>

        <div class="bg-card border border-border/10 rounded-lg p-6 h-fit shadow-sm">
          <h3 class="text-lg font-bold mb-4">Start Your Journey</h3>
          <p class="text-sm text-muted-foreground mb-4">Ready to transform your future? Apply now to join our vibrant academic community.</p>
          <button class="w-full bg-primary text-primary-foreground hover:bg-primary/90 font-bold py-2 px-4 rounded">Apply Now</button>
        </div>
      </div>

    </div>
  </div>
</CollegeLayout>
