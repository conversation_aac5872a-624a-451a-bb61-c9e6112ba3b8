---
import CollegeLayout from '@/layouts/CollegeLayout.astro';
import ProgramCard from '@/components/academics/ProgramCard.astro';
import { programs } from '@/lib/data/programs';

const graduatePrograms = programs.filter(p => p.category === 'Graduate');
---

<CollegeLayout title="Graduate Programs">
  <div class="container mx-auto px-4 py-12 sm:py-16 lg:py-20">
    <div class="text-center mb-12">
      <a href="/" class="text-primary hover:underline mb-4 inline-block">&larr; Back to Home</a>
      <h1 class="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">Graduate Programs</h1>
      <p class="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">Advance your career with our Master's degree programs, designed for working professionals and aspiring leaders.</p>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {graduatePrograms.map(program => (
        <ProgramCard {program} />
      ))}
    </div>
  </div>
</CollegeLayout>
