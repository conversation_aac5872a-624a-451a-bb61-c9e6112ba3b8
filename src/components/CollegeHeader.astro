---
import Logo from "@/components/header/Logo.astro";
import ThemeToggle from "@/components/ThemeToggle.astro";
import { getSchoolSettings } from "@/lib/api";

// Fetch school settings
const schoolSettings = await getSchoolSettings();
const { online_enrollment_enabled, school_year_string, semester } = schoolSettings.data;
---

<header
  id="college-header"
  class="bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-0 z-40 w-full border-b backdrop-blur"
>
  <div
    class="container flex h-16 sm:h-20 items-center justify-between px-4 sm:px-6 lg:px-8 transition-all duration-300"
  >
    <!-- Logo and Brand -->
    <div class="flex items-center gap-2">
      <Logo />
    </div>

    <!-- Desktop Navigation -->
    <div class="hidden lg:flex items-center justify-end space-x-1 ml-auto">
      <nav class="flex items-center">
        <!-- Main Navigation Links -->
        <div class="nav-links flex items-center space-x-1">
          <a href="#about" class="nav-link px-3 py-2 text-sm font-medium rounded-md transition-all duration-300 hover:bg-accent/50 hover:text-primary relative overflow-hidden">
            About
            <span class="nav-indicator absolute bottom-0 left-0 w-full h-0.5 bg-primary scale-x-0 transition-transform duration-300 origin-left"></span>
          </a>

          <!-- Programs Dropdown -->
          <div class="relative group">
            <button class="nav-link px-3 py-2 text-sm font-medium rounded-md transition-all duration-300 hover:bg-accent/50 hover:text-primary relative overflow-hidden flex items-center gap-1">
              Programs
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="transition-transform duration-200 group-hover:rotate-180"><polyline points="6 9 12 15 18 9"></polyline></svg>
              <span class="nav-indicator absolute bottom-0 left-0 w-full h-0.5 bg-primary scale-x-0 transition-transform duration-300 origin-left"></span>
            </button>
            <div class="absolute left-0 mt-1 w-56 origin-top-left rounded-md bg-card shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform group-hover:translate-y-0 translate-y-2">
              <div class="py-1">
                <a href="/academics/undergraduate" class="block px-4 py-2 text-sm hover:bg-accent/50 transition-colors">Undergraduate Programs</a>
                <a href="/academics/graduate" class="block px-4 py-2 text-sm hover:bg-accent/50 transition-colors">Graduate Programs</a>
                <a href="/academics/tesda-courses" class="block px-4 py-2 text-sm hover:bg-accent/50 transition-colors">TESDA Courses</a>
              </div>
            </div>
          </div>

          <!-- Facilities Dropdown -->


          

        <!-- Action Buttons -->
        <div class="ml-4 flex items-center space-x-2">
          <!-- Search Button -->
          <button id="search-button" class="p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent/50 transition-colors duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>
            <span class="sr-only">Search</span>
          </button>

          <!-- Enroll Now Button - Shows modal if enrollment is disabled -->
          {online_enrollment_enabled ? (
            <a
              href="https://portal.dccp.edu.ph/enroll"
              class="apply-button relative overflow-hidden group px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-300 text-sm font-medium"
            >
              <span class="relative z-10">Enroll Now</span>
              <span class="absolute inset-0 bg-primary-foreground/10 scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></span>
            </a>
          ) : (
            <button
              id="enrollment-disabled-btn"
              class="apply-button relative overflow-hidden group px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-300 text-sm font-medium"
            >
              <span class="relative z-10">Enroll Now</span>
              <span class="absolute inset-0 bg-primary-foreground/10 scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></span>
            </button>
          )}

          <!-- Theme Toggle -->
          <ThemeToggle />
        </div>
      </nav>
    </div>

    <!-- Mobile Menu Button -->
    <div class="lg:hidden flex items-center gap-2 ml-auto">
      <button id="search-button-mobile" class="p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent/50 transition-colors duration-300">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>
        <span class="sr-only">Search</span>
      </button>

      <button id="mobile-menu-button" class="p-2 rounded-md hover:bg-accent/50 transition-colors duration-300" aria-label="Open menu" aria-expanded="false">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6"><line x1="4" x2="20" y1="12" y2="12"/><line x1="4" x2="20" y1="6" y2="6"/><line x1="4" x2="20" y1="18" y2="18"/></svg>
        <span class="sr-only">Menu</span>
      </button>
    </div>
  </div>
</header>

<div id="mobile-menu-container" class="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 hidden lg:hidden">
  <div class="fixed inset-y-0 right-0 w-4/5 max-w-sm bg-background border-l shadow-xl h-full overflow-auto">
    <div class="flex flex-col h-full">
      <!-- Header -->
      <div class="flex justify-between items-center p-4 border-b">
        <div class="flex items-center gap-2">
          <div class="rounded-full bg-primary/10 p-1.5">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-primary"><path d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c3 3 9 3 12 0v-5"/></svg>
          </div>
          <span class="text-sm font-medium">DCCP Baguio</span>
        </div>
        <button id="close-mobile-menu" class="p-2 rounded-md hover:bg-accent/50 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
          <span class="sr-only">Close menu</span>
        </button>
      </div>
      
      <!-- Navigation -->
      <nav class="p-4 space-y-4 flex-1 overflow-y-auto">
        <a href="#about" class="block p-3 rounded-md hover:bg-accent/50 transition-colors">About</a>
        
        <!-- Programs dropdown -->
        <div class="mobile-dropdown">
          <button class="w-full flex justify-between items-center p-3 rounded-md hover:bg-accent/50 transition-colors">
            Programs
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="dropdown-icon"><polyline points="6 9 12 15 18 9"></polyline></svg>
          </button>
          <div class="dropdown-content hidden pl-6 mt-1 space-y-2">
            <a href="/academics/undergraduate" class="block p-2 rounded-md hover:bg-accent/50 transition-colors">Undergraduate Programs</a>
            <a href="/academics/graduate" class="block p-2 rounded-md hover:bg-accent/50 transition-colors">Graduate Programs</a>
            <a href="/academics/tesda-courses" class="block p-2 rounded-md hover:bg-accent/50 transition-colors">TESDA Courses</a>
          </div>
        </div>
        
        <!-- Facilities dropdown -->
        <div class="mobile-dropdown">
          <button class="w-full flex justify-between items-center p-3 rounded-md hover:bg-accent/50 transition-colors">
            Facilities
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="dropdown-icon"><polyline points="6 9 12 15 18 9"></polyline></svg>
          </button>
          <div class="dropdown-content hidden pl-6 mt-1 space-y-2">
            <a href="#library" class="block p-2 rounded-md hover:bg-accent/50 transition-colors">Library & Learning Center</a>
            <a href="#labs" class="block p-2 rounded-md hover:bg-accent/50 transition-colors">Laboratories</a>
            <a href="#sports" class="block p-2 rounded-md hover:bg-accent/50 transition-colors">Sports Facilities</a>
            <a href="#campus" class="block p-2 rounded-md hover:bg-accent/50 transition-colors">Campus Tour</a>
          </div>
        </div>
        
        <a href="#admissions" class="block p-3 rounded-md hover:bg-accent/50 transition-colors">Admissions</a>
        <a href="#contact" class="block p-3 rounded-md hover:bg-accent/50 transition-colors">Contact</a>
        <a href="/dccp-hub" class="block p-3 rounded-md hover:bg-accent/50 transition-colors bg-primary/5">
          <div class="flex items-center gap-1">
            <span>DCCPHub</span>
            <div class="h-1.5 w-1.5 rounded-full bg-primary animate-pulse"></div>
          </div>
        </a>
      </nav>
      
      <!-- Footer -->
      <div class="p-4 border-t">
        {online_enrollment_enabled ? (
          <a href="https://portal.dccp.edu.ph/enroll" class="block w-full py-2 px-3 bg-primary text-primary-foreground text-center rounded-md mb-4">
            Enroll Now
          </a>
        ) : (
          <button id="enrollment-disabled-btn-mobile" class="block w-full py-2 px-3 bg-primary text-primary-foreground text-center rounded-md mb-4">
            Enroll Now
          </button>
        )}
        <div class="flex justify-between items-center">
          <span class="text-sm text-muted-foreground">© 2023 DCCP Baguio</span>
          <button id="mobile-theme-toggle" class="p-2 rounded-md hover:bg-accent/50 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 dark:hidden"><circle cx="12" cy="12" r="4"/><path d="M12 2v2"/><path d="M12 20v2"/><path d="m4.93 4.93 1.41 1.41"/><path d="m17.66 17.66 1.41 1.41"/><path d="M2 12h2"/><path d="M20 12h2"/><path d="m6.34 17.66-1.41 1.41"/><path d="m19.07 4.93-1.41 1.41"/></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 hidden dark:block"><path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"/></svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Search Overlay -->
<div id="search-overlay" class="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 hidden">
  <div class="h-full flex items-center justify-center p-4">
    <div class="bg-card rounded-lg shadow-xl max-w-md w-full p-6 opacity-0 scale-95 transition-all duration-300" id="search-container">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium">Search</h3>
        <button id="close-search" class="p-1 rounded-md hover:bg-accent/50 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
        </button>
      </div>
      <div class="relative">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>
        <input type="text" placeholder="Search for programs, facilities..." class="w-full pl-10 pr-4 py-2 rounded-md border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all">
      </div>
      <div class="mt-4">
        <p class="text-sm text-muted-foreground">Popular searches:</p>
        <div class="flex flex-wrap gap-2 mt-2">
          <a href="#admissions" class="text-xs px-2 py-1 rounded-md bg-accent hover:bg-accent/80 transition-colors">Admissions</a>
          <a href="#programs" class="text-xs px-2 py-1 rounded-md bg-accent hover:bg-accent/80 transition-colors">Computer Science</a>
          <a href="#facilities" class="text-xs px-2 py-1 rounded-md bg-accent hover:bg-accent/80 transition-colors">Library</a>
          <a href="#contact" class="text-xs px-2 py-1 rounded-md bg-accent hover:bg-accent/80 transition-colors">Contact</a>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Enrollment Disabled Modal -->
<div id="enrollment-disabled-modal" class="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 hidden">
  <div class="h-full flex items-center justify-center p-4">
    <div class="bg-card rounded-lg shadow-xl max-w-md w-full p-6 opacity-0 scale-95 transition-all duration-300" id="enrollment-modal-container">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium">Enrollment Notice</h3>
        <button id="close-enrollment-modal" class="p-1 rounded-md hover:bg-accent/50 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
        </button>
      </div>
      <div class="mb-6">
        <div class="flex items-center gap-3 mb-4">
          <div class="rounded-full bg-primary/10 p-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-primary">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" x2="12" y1="8" y2="12"/>
              <line x1="12" x2="12.01" y1="16" y2="16"/>
            </svg>
          </div>
          <p class="font-medium">Online Enrollment Currently Unavailable</p>
        </div>
        <p class="text-muted-foreground">We're sorry, but online enrollment is not available at this time. Please check back later or contact our admissions office for assistance.</p>
        <p class="mt-2 text-sm text-muted-foreground">For inquiries, you can reach us at <a href="mailto:<EMAIL>" class="text-primary hover:underline"><EMAIL></a> or call us at <a href="tel:+63744420000" class="text-primary hover:underline">+63 74 442 0000</a>.</p>
      </div>
      <div class="flex justify-end">
        <button id="confirm-enrollment-modal" class="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors">
          I Understand
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const header = document.getElementById('college-header');
    const navLinks = document.querySelectorAll('.nav-link');
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const closeMobileMenuButton = document.getElementById('close-mobile-menu');
    const mobileMenuContainer = document.getElementById('mobile-menu-container');
    const mobileDropdowns = document.querySelectorAll('.mobile-dropdown');
    const searchButton = document.getElementById('search-button');
    const searchButtonMobile = document.getElementById('search-button-mobile');
    const searchOverlay = document.getElementById('search-overlay');
    const searchContainer = document.getElementById('search-container');
    const closeSearchButton = document.getElementById('close-search');
    const mobileThemeToggle = document.getElementById('mobile-theme-toggle');
    
    // Enrollment disabled modal elements
    const enrollmentDisabledBtn = document.getElementById('enrollment-disabled-btn');
    const enrollmentDisabledBtnMobile = document.getElementById('enrollment-disabled-btn-mobile');
    const enrollmentDisabledModal = document.getElementById('enrollment-disabled-modal');
    const enrollmentModalContainer = document.getElementById('enrollment-modal-container');
    const closeEnrollmentModalBtn = document.getElementById('close-enrollment-modal');
    const confirmEnrollmentModalBtn = document.getElementById('confirm-enrollment-modal');
    
    // Scroll effect for header
    let lastScrollY = window.scrollY;
    
    function updateHeaderOnScroll() {
      if (window.scrollY > 50) {
        header.classList.add('shadow-sm');
      } else {
        header.classList.remove('shadow-sm');
      }
      
      // Hide header on scroll down, show on scroll up
      if (window.scrollY > lastScrollY && window.scrollY > 150) {
        header.style.transform = 'translateY(-100%)';
      } else {
        header.style.transform = 'translateY(0)';
      }
      lastScrollY = window.scrollY;
    }
    
    window.addEventListener('scroll', updateHeaderOnScroll);
    
    // Mobile menu
    function openMobileMenu() {
      document.body.style.overflow = 'hidden';
      mobileMenuContainer.classList.remove('hidden');
    }
    
    function closeMobileMenu() {
      document.body.style.overflow = '';
      mobileMenuContainer.classList.add('hidden');
    }
    
    mobileMenuButton.addEventListener('click', openMobileMenu);
    closeMobileMenuButton.addEventListener('click', closeMobileMenu);
    
    // Close menu when clicking outside
    mobileMenuContainer.addEventListener('click', function(e) {
      if (e.target === mobileMenuContainer) {
        closeMobileMenu();
      }
    });
    
    // Mobile dropdowns
    mobileDropdowns.forEach(dropdown => {
      const button = dropdown.querySelector('button');
      const content = dropdown.querySelector('.dropdown-content');
      const icon = dropdown.querySelector('.dropdown-icon');
      
      button.addEventListener('click', () => {
        content.classList.toggle('hidden');
        if (content.classList.contains('hidden')) {
          icon.style.transform = '';
        } else {
          icon.style.transform = 'rotate(180deg)';
        }
      });
    });
    
    // Search overlay
    function openSearch() {
      document.body.style.overflow = 'hidden';
      searchOverlay.classList.remove('hidden');
      setTimeout(() => {
        searchContainer.style.opacity = '1';
        searchContainer.style.transform = 'scale(1)';
      }, 10);
    }
    
    function closeSearch() {
      searchContainer.style.opacity = '0';
      searchContainer.style.transform = 'scale(0.95)';
      setTimeout(() => {
        searchOverlay.classList.add('hidden');
        document.body.style.overflow = '';
      }, 300);
    }
    
    searchButton?.addEventListener('click', openSearch);
    searchButtonMobile?.addEventListener('click', openSearch);
    closeSearchButton?.addEventListener('click', closeSearch);
    
    searchOverlay.addEventListener('click', (e) => {
      if (e.target === searchOverlay) {
        closeSearch();
      }
    });
    
    // Close with Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        if (!searchOverlay.classList.contains('hidden')) {
          closeSearch();
        } else if (!mobileMenuContainer.classList.contains('hidden')) {
          closeMobileMenu();
        }
      }
    });
    
    // Theme toggle
    if (mobileThemeToggle) {
      mobileThemeToggle.addEventListener('click', () => {
        const isDark = document.documentElement.classList.toggle('dark');
        localStorage.setItem('theme', isDark ? 'dark' : 'light');
      });
    }
    
    // Nav indicator effect
    navLinks.forEach(link => {
      link.addEventListener('mouseenter', () => {
        const indicator = link.querySelector('.nav-indicator');
        if (indicator) indicator.style.transform = 'scaleX(1)';
      });
      
      link.addEventListener('mouseleave', () => {
        const indicator = link.querySelector('.nav-indicator');
        if (indicator) indicator.style.transform = 'scaleX(0)';
      });
    });
    
    // Enrollment disabled modal functions
    function openEnrollmentModal() {
      document.body.style.overflow = 'hidden';
      if (enrollmentDisabledModal) {
        enrollmentDisabledModal.classList.remove('hidden');
        setTimeout(() => {
          if (enrollmentModalContainer) {
            enrollmentModalContainer.style.opacity = '1';
            enrollmentModalContainer.style.transform = 'scale(1)';
          }
        }, 10);
      }
    }
    
    function closeEnrollmentModal() {
      if (enrollmentModalContainer) {
        enrollmentModalContainer.style.opacity = '0';
        enrollmentModalContainer.style.transform = 'scale(0.95)';
        setTimeout(() => {
          if (enrollmentDisabledModal) {
            enrollmentDisabledModal.classList.add('hidden');
            document.body.style.overflow = '';
          }
        }, 300);
      }
    }
    
    // Add event listeners for enrollment modal
    if (enrollmentDisabledBtn) {
      enrollmentDisabledBtn.addEventListener('click', openEnrollmentModal);
    }
    
    if (enrollmentDisabledBtnMobile) {
      enrollmentDisabledBtnMobile.addEventListener('click', openEnrollmentModal);
    }
    
    if (closeEnrollmentModalBtn) {
      closeEnrollmentModalBtn.addEventListener('click', closeEnrollmentModal);
    }
    
    if (confirmEnrollmentModalBtn) {
      confirmEnrollmentModalBtn.addEventListener('click', closeEnrollmentModal);
    }
    
    if (enrollmentDisabledModal) {
      enrollmentDisabledModal.addEventListener('click', (e) => {
        if (e.target === enrollmentDisabledModal) {
          closeEnrollmentModal();
        }
      });
    }
  });
</script>

<style>
  /* Header transitions */
  #college-header {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  /* Nav links effects */
  .nav-link:hover .nav-indicator {
    transform: scaleX(1) !important;
  }
  
  /* Apply button effects */
  .apply-button:hover span:last-child {
    transform: scaleX(1);
  }
  
  /* Dropdown icon transition */
  .dropdown-icon {
    transition: transform 0.2s ease;
  }
  
  /* Search container animation */
  #search-container,
  #enrollment-modal-container {
    transition: opacity 0.3s ease, transform 0.3s ease;
  }
  
  /* Mobile optimizations */
  @media (max-width: 640px) {
    #college-header > div {
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
    }
  }
</style>