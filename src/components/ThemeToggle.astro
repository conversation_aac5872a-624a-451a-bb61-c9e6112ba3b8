---
import { SunIcon, MoonIcon } from "@radix-ui/react-icons";
import { Button } from "@/components/ui/button";
---

<Button variant="ghost" size="icon" id="themeToggle">
  <SunIcon className="h-[1.5rem] w-[1.3rem] dark:hidden" />
  <MoonIcon className="hidden h-5 w-5 dark:block" />
  <span class="sr-only">Toggle theme</span>
</Button>

<script is:inline>
  const getThemePreference = () => {
    if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
      return localStorage.getItem('theme')
    }
    return 'light'
  }
  const isDark = getThemePreference() === 'dark'
  document.documentElement.classList[isDark ? 'add' : 'remove']('dark')

  if (typeof localStorage !== 'undefined') {
    const observer = new MutationObserver(() => {
      const isDark = document.documentElement.classList.contains('dark')
      localStorage.setItem('theme', isDark ? 'dark' : 'light')
    })
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class'],
    })
  }

  const handleToggleClick = () => {
    const element = document.documentElement
    element.classList.toggle('dark')

    const isDark = element.classList.contains('dark')
    localStorage.setItem('theme', isDark ? 'dark' : 'light')
  }

  document
    .getElementById('themeToggle')
    .addEventListener('click', handleToggleClick)
</script>
