---
import { Badge } from "@/components/ui/badge";
---

<section class="py-20 relative overflow-hidden">
  <!-- Background elements -->
  <div class="absolute inset-0 -z-10">
    <div class="absolute inset-0 bg-background"></div>
    <!-- Grid pattern background -->
    <div class="absolute inset-0 opacity-[0.02]" style="background-size: 40px 40px; background-image: linear-gradient(to right, var(--border) 1px, transparent 1px), linear-gradient(to bottom, var(--border) 1px, transparent 1px);"></div>
  </div>

  <div class="container">
    <!-- Section header -->
    <div class="text-center max-w-3xl mx-auto mb-16">
      <Badge variant="outline" className="mb-4">Interface</Badge>
      <h2 class="text-3xl md:text-4xl font-bold tracking-tight mb-4">Intuitive Design for Everyone</h2>
      <p class="text-muted-foreground text-lg">
        DCCPHub features a clean, modern interface that's easy to navigate for both students and teachers.
      </p>
    </div>

    <!-- Custom Tabs for different user interfaces -->
    <div class="interface-tabs w-full">
      <!-- Tab buttons -->
      <div class="flex justify-center mb-8">
        <div class="w-full max-w-md grid grid-cols-2 bg-muted rounded-lg p-1">
          <button
            id="student-tab"
            class="tab-button active py-3 px-4 rounded-md text-sm md:text-base font-medium transition-all duration-300"
            data-tab="student"
          >
            Student View
          </button>
          <button
            id="teacher-tab"
            class="tab-button py-3 px-4 rounded-md text-sm md:text-base font-medium transition-all duration-300"
            data-tab="teacher"
          >
            Teacher View
          </button>
        </div>
      </div>

      <!-- Tab content -->
      <div class="tab-content-container">
        <!-- Student Interface -->
        <div id="student-content" class="tab-content active">
          <div class="grid md:grid-cols-2 gap-12 items-center">
            <div class="order-2 md:order-1">
              <h3 class="text-2xl font-semibold mb-4">Student Dashboard</h3>
              <p class="text-muted-foreground mb-6">
                Students enjoy a personalized dashboard that provides quick access to courses, assignments, grades, and important announcements.
              </p>

              <ul class="space-y-4">
                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Course Overview</h4>
                    <p class="text-sm text-muted-foreground">Quick access to all enrolled courses with progress indicators</p>
                  </div>
                </li>

                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Assignment Tracker</h4>
                    <p class="text-sm text-muted-foreground">Upcoming and past assignments with due dates and status</p>
                  </div>
                </li>

                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Grade Visualization</h4>
                    <p class="text-sm text-muted-foreground">Visual charts and graphs showing academic performance</p>
                  </div>
                </li>

                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Notification Center</h4>
                    <p class="text-sm text-muted-foreground">Important alerts for deadlines, grades, and announcements</p>
                  </div>
                </li>
              </ul>
            </div>

            <div class="order-1 md:order-2 interface-image">
              <div class="relative mx-auto max-w-[500px]">
                <!-- Browser window mockup -->
                <div class="rounded-xl overflow-hidden shadow-lg border border-border/50 bg-card">
                  <!-- Browser content -->
                  <img
                    src="/images/portal/student-dashboard.png"
                    alt="Student Dashboard Interface"
                    class="w-full h-auto"
                  />
                </div>

                <!-- Decorative elements -->
                <div class="absolute -bottom-4 -right-4 h-20 w-20 rounded-full bg-primary/10 blur-xl"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Teacher Interface -->
        <div id="teacher-content" class="tab-content hidden">
          <div class="grid md:grid-cols-2 gap-12 items-center">
            <div class="order-2 md:order-1">
              <h3 class="text-2xl font-semibold mb-4">Teacher Dashboard</h3>
              <p class="text-muted-foreground mb-6">
                Teachers have powerful tools to manage courses, track student progress, grade assignments, and communicate effectively.
              </p>

              <ul class="space-y-4">
                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Course Management</h4>
                    <p class="text-sm text-muted-foreground">Create and organize course content, materials, and assignments</p>
                  </div>
                </li>

                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Grading Tools</h4>
                    <p class="text-sm text-muted-foreground">Efficient grading interface with rubrics and feedback options</p>
                  </div>
                </li>

                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Student Analytics</h4>
                    <p class="text-sm text-muted-foreground">Detailed insights into student performance and engagement</p>
                  </div>
                </li>

                <li class="flex gap-3">
                  <div class="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                  </div>
                  <div>
                    <h4 class="text-base font-medium">Communication Center</h4>
                    <p class="text-sm text-muted-foreground">Tools for announcements, messaging, and discussion moderation</p>
                  </div>
                </li>
              </ul>
            </div>

            <div class="order-1 md:order-2 interface-image">
              <div class="relative mx-auto max-w-[500px]">
                <!-- Browser window mockup -->
                <div class="rounded-xl overflow-hidden shadow-lg border border-border/50 bg-card">
                  <!-- Browser content -->
                  <img
                    src="/images/portal/teacher-dashboard.png"
                    alt="Teacher Dashboard Interface"
                    class="w-full h-auto"
                  />
                </div>

                <!-- Decorative elements -->
                <div class="absolute -bottom-4 -left-4 h-20 w-20 rounded-full bg-primary/10 blur-xl"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile view showcase -->
    <div class="mt-24 flex flex-col items-center justify-center gap-8 relative overflow-visible min-h-[400px]">
      <!-- Elegant blurred/gradient background accent -->
      <div class="absolute inset-0 flex items-center justify-center pointer-events-none -z-10">
        <div class="w-3/4 h-2/3 bg-gradient-to-tr from-primary/20 via-primary/5 to-transparent blur-3xl rounded-full opacity-70 animate-gradient-move"></div>
      </div>
      <div class="text-center space-y-6">
        <span class="inline-block px-4 py-1 rounded-full bg-primary text-primary-foreground text-sm font-semibold shadow-lg animate-pulse-glow mb-2">In Development</span>
        <h3 class="text-3xl md:text-5xl font-extrabold tracking-tight bg-gradient-to-r from-primary via-blue-400 to-primary bg-clip-text text-transparent animate-gradient-shimmer">Mobile App Coming Soon</h3>
        <p class="text-muted-foreground text-lg max-w-xl mx-auto animate-fade-in">
          We're building a next-generation mobile experience for DCCPHub.<br />Stay tuned for updates and early access!
        </p>
        <!-- Optional: animated sparkles -->
        <div class="flex justify-center mt-4">
          <span class="inline-block w-2 h-2 rounded-full bg-primary/80 animate-sparkle mx-1"></span>
          <span class="inline-block w-1.5 h-1.5 rounded-full bg-primary/60 animate-sparkle-delay mx-1"></span>
          <span class="inline-block w-2.5 h-2.5 rounded-full bg-primary/40 animate-sparkle-delay2 mx-1"></span>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Tab functionality
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tabId = button.getAttribute('data-tab');

        // Update active tab button
        tabButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');

        // Show corresponding content
        tabContents.forEach(content => {
          if (content.id === `${tabId}-content`) {
            content.classList.remove('hidden');
            content.classList.add('active');
          } else {
            content.classList.add('hidden');
            content.classList.remove('active');
          }
        });
      });
    });

    // Animation for images
    const interfaceImages = document.querySelectorAll('.interface-image');
    const mobileShowcase = document.querySelector('.mobile-showcase');

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.1 });

    interfaceImages.forEach(image => {
      observer.observe(image);
    });

    if (mobileShowcase) {
      observer.observe(mobileShowcase);
    }
  });
</script>

<style>
  /* Tab styles */
  .tab-button {
    background-color: transparent;
    color: var(--muted-foreground);
  }

  .tab-button.active {
    background-color: var(--background);
    color: var(--foreground);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* Tab content animation */
  .tab-content {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.5s ease, transform 0.5s ease;
  }

  .tab-content.active {
    opacity: 1;
    transform: translateY(0);
  }

  /* Image animations */
  .interface-image, .mobile-showcase {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.8s ease, transform 0.8s ease;
  }

  .animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes gradient-move {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-10px) scale(1.05); }
  }
  .animate-gradient-move {
    animation: gradient-move 8s ease-in-out infinite;
  }
  @keyframes gradient-shimmer {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
  }
  .animate-gradient-shimmer {
    background-size: 200% 200%;
    animation: gradient-shimmer 3s linear infinite;
  }
  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 0 0 rgba(59,130,246,0.3); }
    50% { box-shadow: 0 0 16px 4px rgba(59,130,246,0.5); }
  }
  .animate-pulse-glow {
    animation: pulse-glow 2s infinite;
  }
  @keyframes fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  .animate-fade-in {
    animation: fade-in 1.2s cubic-bezier(0.16, 1, 0.3, 1) both;
  }
  @keyframes sparkle {
    0%, 100% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.3); }
  }
  .animate-sparkle {
    animation: sparkle 2.2s infinite;
  }
  .animate-sparkle-delay {
    animation: sparkle 2.2s 0.7s infinite;
  }
  .animate-sparkle-delay2 {
    animation: sparkle 2.2s 1.2s infinite;
  }
</style>
