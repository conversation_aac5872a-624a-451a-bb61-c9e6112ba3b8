---
import { But<PERSON> } from "@/components/ui/button";
---

<section class="py-20 relative overflow-hidden">
  <!-- Background elements -->
  <div class="absolute inset-0 -z-10">
    <div class="absolute inset-0 bg-background"></div>
    <!-- Grid pattern background -->
    <div class="absolute inset-0 opacity-[0.02]" style="background-size: 40px 40px; background-image: linear-gradient(to right, var(--border) 1px, transparent 1px), linear-gradient(to bottom, var(--border) 1px, transparent 1px);"></div>
    
    <!-- Decorative elements -->
    <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 right-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl"></div>
  </div>

  <div class="container">
    <div class="max-w-4xl mx-auto">
      <!-- CTA Card -->
      <div class="cta-card relative rounded-2xl overflow-hidden border shadow-lg bg-card">
        <!-- Decorative gradient overlay -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/5 pointer-events-none"></div>
        
        <div class="relative z-10 p-8 md:p-12 text-center">
          <h2 class="text-3xl md:text-4xl font-bold tracking-tight mb-4">Ready to Experience DCCPHub?</h2>
          <p class="text-muted-foreground text-lg mb-8 max-w-2xl mx-auto">
            Join thousands of students and teachers who are already benefiting from our comprehensive academic portal.
          </p>
          
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="group relative overflow-hidden">
              <span class="relative z-10">Log In to Portal</span>
              <span class="absolute inset-0 -z-10 bg-gradient-to-r from-primary to-primary/80 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></span>
            </Button>
            
            <Button variant="outline" size="lg">
              Request a Demo
            </Button>
          </div>
          
          <!-- Access options -->
          <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="access-option p-4 rounded-lg bg-muted/50 text-center">
              <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
              </div>
              <h3 class="text-lg font-medium mb-2">Student Access</h3>
              <p class="text-sm text-muted-foreground">
                Use your student ID and password to access your personalized dashboard.
              </p>
            </div>
            
            <div class="access-option p-4 rounded-lg bg-muted/50 text-center">
              <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
              </div>
              <h3 class="text-lg font-medium mb-2">Teacher Access</h3>
              <p class="text-sm text-muted-foreground">
                Faculty members can log in with their institutional credentials.
              </p>
            </div>
            
            <div class="access-option p-4 rounded-lg bg-muted/50 text-center">
              <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>
              </div>
              <h3 class="text-lg font-medium mb-2">Guest Access</h3>
              <p class="text-sm text-muted-foreground">
                Request a demo account to explore DCCPHub's features.
              </p>
            </div>
          </div>
          
          <!-- Support info -->
          <div class="mt-12 pt-8 border-t">
            <h3 class="text-lg font-medium mb-4">Need Help?</h3>
            <div class="flex flex-col md:flex-row gap-6 justify-center">
              <div class="flex items-center gap-2 justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>
                <span class="text-sm">+63 (74) 123-4567</span>
              </div>
              
              <div class="flex items-center gap-2 justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg>
                <span class="text-sm"><EMAIL></span>
              </div>
              
              <div class="flex items-center gap-2 justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
                <span class="text-sm">Live Chat Support</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const ctaCard = document.querySelector('.cta-card');
    const accessOptions = document.querySelectorAll('.access-option');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.1 });
    
    if (ctaCard) {
      observer.observe(ctaCard);
    }
    
    accessOptions.forEach((option, index) => {
      setTimeout(() => {
        option.classList.add('option-visible');
      }, 300 + (index * 100)); // Staggered animation
    });
  });
</script>

<style>
  .cta-card {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.8s ease, transform 0.8s ease;
  }
  
  .animate-in {
    opacity: 1;
    transform: translateY(0);
  }
  
  .access-option {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.5s ease, transform 0.5s ease;
  }
  
  .option-visible {
    opacity: 1;
    transform: translateY(0);
  }
</style>
