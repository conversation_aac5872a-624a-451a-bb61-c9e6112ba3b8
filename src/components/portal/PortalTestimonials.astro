---
import { Badge } from "@/components/ui/badge";
---

<section class="py-20 bg-muted/30">
  <div class="container">
    <!-- Section header -->
    <div class="text-center max-w-3xl mx-auto mb-16">
      <Badge variant="outline" className="mb-4">Testimonials</Badge>
      <h2 class="text-3xl md:text-4xl font-bold tracking-tight mb-4">What Our Users Say</h2>
      <p class="text-muted-foreground text-lg">
        Hear from students and teachers who use DCCPHub every day to enhance their academic experience.
      </p>
    </div>
    
    <!-- Testimonials grid -->
    <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
      <!-- Student Testimonial 1 -->
      <div class="testimonial-card bg-background rounded-xl p-6 border shadow-sm">
        <div class="flex items-start gap-4 mb-4">
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium text-lg">
            JD
          </div>
          <div>
            <h3 class="font-medium"><PERSON></h3>
            <p class="text-sm text-muted-foreground">Computer Science Student</p>
          </div>
        </div>
        
        <div class="mb-4">
          <div class="flex text-amber-400 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
          </div>
        </div>
        
        <blockquote class="text-muted-foreground">
          <p>"DCCPHub has completely transformed how I manage my coursework. I can access all my materials, submit assignments, and check grades from anywhere. The mobile app is especially helpful when I'm on the go."</p>
        </blockquote>
        
        <div class="mt-4 pt-4 border-t text-sm text-muted-foreground">
          <p>Using DCCPHub since 2022</p>
        </div>
      </div>
      
      <!-- Teacher Testimonial -->
      <div class="testimonial-card bg-background rounded-xl p-6 border shadow-sm">
        <div class="flex items-start gap-4 mb-4">
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium text-lg">
            MS
          </div>
          <div>
            <h3 class="font-medium">Maria Santos, PhD</h3>
            <p class="text-sm text-muted-foreground">Computer Science Professor</p>
          </div>
        </div>
        
        <div class="mb-4">
          <div class="flex text-amber-400 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
          </div>
        </div>
        
        <blockquote class="text-muted-foreground">
          <p>"As an instructor, DCCPHub has streamlined my course management process. The grading tools are intuitive, and the analytics help me identify students who may need additional support. It's saved me countless hours of administrative work."</p>
        </blockquote>
        
        <div class="mt-4 pt-4 border-t text-sm text-muted-foreground">
          <p>Using DCCPHub since 2021</p>
        </div>
      </div>
      
      <!-- Student Testimonial 2 -->
      <div class="testimonial-card bg-background rounded-xl p-6 border shadow-sm">
        <div class="flex items-start gap-4 mb-4">
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium text-lg">
            AR
          </div>
          <div>
            <h3 class="font-medium">Anna Reyes</h3>
            <p class="text-sm text-muted-foreground">Business Administration Student</p>
          </div>
        </div>
        
        <div class="mb-4">
          <div class="flex text-amber-400 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
          </div>
        </div>
        
        <blockquote class="text-muted-foreground">
          <p>"The discussion forums in DCCPHub have been invaluable for collaborating with classmates on group projects. I also love how the calendar syncs with my phone, so I never miss an important deadline or class."</p>
        </blockquote>
        
        <div class="mt-4 pt-4 border-t text-sm text-muted-foreground">
          <p>Using DCCPHub since 2022</p>
        </div>
      </div>
    </div>
    
    <!-- Stats section -->
    <div class="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8">
      <div class="stat-card text-center p-6 rounded-xl bg-background border">
        <div class="text-4xl font-bold text-primary mb-2">98%</div>
        <p class="text-sm text-muted-foreground">User Satisfaction</p>
      </div>
      
      <div class="stat-card text-center p-6 rounded-xl bg-background border">
        <div class="text-4xl font-bold text-primary mb-2">100%</div>
        <p class="text-sm text-muted-foreground">Course Adoption</p>
      </div>
      
      <div class="stat-card text-center p-6 rounded-xl bg-background border">
        <div class="text-4xl font-bold text-primary mb-2">15%</div>
        <p class="text-sm text-muted-foreground">Improved Grades</p>
      </div>
      
      <div class="stat-card text-center p-6 rounded-xl bg-background border">
        <div class="text-4xl font-bold text-primary mb-2">30%</div>
        <p class="text-sm text-muted-foreground">Time Saved</p>
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const testimonialCards = document.querySelectorAll('.testimonial-card');
    const statCards = document.querySelectorAll('.stat-card');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            entry.target.classList.add('card-visible');
          }, index * 100); // Staggered animation
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.1 });
    
    testimonialCards.forEach(card => {
      observer.observe(card);
    });
    
    statCards.forEach(card => {
      observer.observe(card);
    });
  });
</script>

<style>
  .testimonial-card, .stat-card {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
  }
  
  .card-visible {
    opacity: 1;
    transform: translateY(0);
  }
</style>
