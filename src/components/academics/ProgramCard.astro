---
import type { CollectionEntry } from 'astro:content';

interface Props {
  program: any;
}

const { program } = Astro.props;
---

<a href={program.learnMoreLink} class="group block rounded-lg overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 bg-card transform hover:-translate-y-1 border border-border/10 hover:border-primary/20">
  <div class="relative">
    <img src={program.image} alt={`Image for ${program.title}`} class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105" />
    <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
    <div class="absolute bottom-4 left-4">
      <h3 class="text-xl font-bold text-white tracking-tight">{program.title}</h3>
      <p class="text-sm text-white/80">{program.degree}</p>
    </div>
    {program.isPopular && <span class="absolute top-3 right-3 bg-primary text-primary-foreground text-xs font-semibold px-2 py-1 rounded-full">Popular</span>}
    {program.isNew && <span class="absolute top-3 right-3 bg-green-500 text-white text-xs font-semibold px-2 py-1 rounded-full">New</span>}
  </div>
  <div class="p-6">
    <p class="text-muted-foreground text-sm mb-4 h-20 overflow-hidden">{program.description}</p>
    <div class="flex justify-between items-center text-sm text-muted-foreground">
      <div class="flex items-center gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
        <span>{program.duration}</span>
      </div>
      <div class="flex items-center gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><map-pin/></svg>
        <span>{program.format}</span>
      </div>
    </div>
    <div class="mt-4 pt-4 border-t border-border/10 text-center">
      <span class="text-primary font-semibold group-hover:underline">Learn More &rarr;</span>
    </div>
  </div>
</a>
