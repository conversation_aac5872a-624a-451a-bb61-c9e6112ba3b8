---
import { buttonVariants } from "@/components/ui/button";
import ThemeToggle from "@/components/ThemeToggle.astro";
---

<header
  class="bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 w-full border-b backdrop-blur"
>
  <div
    class="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0"
  >
    <div class="flex gap-6 md:gap-10">
      <a href="/" class="flex items-center space-x-2">
        <span class="inline-block font-bold">Home</span>
      </a>
    </div>
    <div class="flex flex-1 items-center justify-end space-x-4">
      <nav class="flex items-center space-x-1">
        <a
          href="https://github.com/AREA44/astro-shadcn-ui-template"
          target="_blank"
          rel="noreferrer"
          class={buttonVariants({ variant: 'ghost' })}
        >
          GitHub
        </a>
        <ThemeToggle />
      </nav>
    </div>
  </div>
</header>
