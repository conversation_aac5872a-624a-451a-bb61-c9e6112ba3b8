---
import { buttonVariants } from "@/components/ui/button";
---

<section id="contact" class="bg-muted/50 py-16 md:py-24">
  <div class="container">
    <div class="mx-auto max-w-[58rem] text-center">
      <h2 class="text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl">Contact Us</h2>
      <p class="mt-4 text-muted-foreground">
        Have questions? We're here to help you on your educational journey
      </p>
    </div>

    <div class="mt-16 grid gap-8 md:grid-cols-2">
      <div>
        <div class="rounded-lg border bg-card p-8 shadow-sm">
          <h3 class="text-2xl font-bold">Get in Touch</h3>
          <div class="mt-6 space-y-4">
            <div class="flex items-start gap-3">
              <div class="rounded-full bg-primary/10 p-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-primary"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/></svg>
              </div>
              <div>
                <h4 class="font-bold">Phone</h4>
                <p class="text-muted-foreground">(*************</p>
                <p class="text-muted-foreground">+63 ************</p>
              </div>
            </div>

            <div class="flex items-start gap-3">
              <div class="rounded-full bg-primary/10 p-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-primary"><rect width="20" height="16" x="2" y="4" rx="2"/><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"/></svg>
              </div>
              <div>
                <h4 class="font-bold">Email</h4>
                <p class="text-muted-foreground"><EMAIL></p>
                <p class="text-muted-foreground"><EMAIL></p>
              </div>
            </div>

            <div class="flex items-start gap-3">
              <div class="rounded-full bg-primary/10 p-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-primary"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/><circle cx="12" cy="10" r="3"/></svg>
              </div>
              <div>
                <h4 class="font-bold">Address</h4>
                <p class="text-muted-foreground">123 Session Road</p>
                <p class="text-muted-foreground">Baguio City, 2600 Philippines</p>
              </div>
            </div>

            <div class="flex items-start gap-3">
              <div class="rounded-full bg-primary/10 p-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-primary"><circle cx="12" cy="12" r="10"/><polyline points="12 6 12 12 16 14"/></svg>
              </div>
              <div>
                <h4 class="font-bold">Office Hours</h4>
                <p class="text-muted-foreground">Monday - Friday: 8:00 AM - 5:00 PM</p>
                <p class="text-muted-foreground">Saturday: 8:00 AM - 12:00 PM</p>
              </div>
            </div>
          </div>

          <div class="mt-8">
            <h4 class="font-bold">Connect With Us</h4>
            <div class="mt-4 flex space-x-4">
              <a href="#" class="rounded-full bg-muted p-2 text-muted-foreground hover:bg-primary hover:text-primary-foreground">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"/></svg>
                <span class="sr-only">Facebook</span>
              </a>
              <a href="#" class="rounded-full bg-muted p-2 text-muted-foreground hover:bg-primary hover:text-primary-foreground">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><rect width="20" height="20" x="2" y="2" rx="5" ry="5"/><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"/><line x1="17.5" x2="17.51" y1="6.5" y2="6.5"/></svg>
                <span class="sr-only">Instagram</span>
              </a>
              <a href="#" class="rounded-full bg-muted p-2 text-muted-foreground hover:bg-primary hover:text-primary-foreground">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"/></svg>
                <span class="sr-only">Twitter</span>
              </a>
              <a href="#" class="rounded-full bg-muted p-2 text-muted-foreground hover:bg-primary hover:text-primary-foreground">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/><rect width="4" height="12" x="2" y="9"/><circle cx="4" cy="4" r="2"/></svg>
                <span class="sr-only">LinkedIn</span>
              </a>
              <a href="#" class="rounded-full bg-muted p-2 text-muted-foreground hover:bg-primary hover:text-primary-foreground">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17"/><path d="m10 15 5-3-5-3z"/></svg>
                <span class="sr-only">YouTube</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <div>
        <div class="rounded-lg border bg-card p-8 shadow-sm">
          <h3 class="text-2xl font-bold">Send Us a Message</h3>
          <form class="mt-6 space-y-4">
            <div class="grid gap-4 md:grid-cols-2">
              <div>
                <label for="name" class="mb-2 block text-sm font-medium">Name</label>
                <input
                  type="text"
                  id="name"
                  placeholder="Your name"
                  class="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                />
              </div>
              <div>
                <label for="email" class="mb-2 block text-sm font-medium">Email</label>
                <input
                  type="email"
                  id="email"
                  placeholder="Your email"
                  class="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                />
              </div>
            </div>
            <div>
              <label for="subject" class="mb-2 block text-sm font-medium">Subject</label>
              <input
                type="text"
                id="subject"
                placeholder="Message subject"
                class="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
              />
            </div>
            <div>
              <label for="message" class="mb-2 block text-sm font-medium">Message</label>
              <textarea
                id="message"
                placeholder="Your message"
                rows="5"
                class="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
              ></textarea>
            </div>
            <button type="submit" class={buttonVariants()}>
              Send Message
            </button>
          </form>
        </div>

        <div class="mt-8 rounded-lg border bg-card p-8 shadow-sm">
          <h3 class="text-2xl font-bold">Campus Map</h3>
          <div class="mt-4 aspect-video w-full overflow-hidden rounded-md bg-muted">
            <!-- This would be replaced with an actual map embed -->
            <div class="flex h-full w-full items-center justify-center bg-primary/10">
              <p class="text-center text-muted-foreground">Interactive Campus Map</p>
            </div>
          </div>
          <p class="mt-4 text-sm text-muted-foreground">
            Our campus is conveniently located in the heart of Baguio City, easily accessible by public transportation. Visit us to experience our facilities firsthand.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>
