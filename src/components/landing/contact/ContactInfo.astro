---
// Define contact information for animation
const contactInfo = [
  {
    icon: "phone",
    title: "Phone",
    items: [
      "(*************",
      "+63 ************"
    ]
  },
  {
    icon: "mail",
    title: "Email",
    items: [
      "<EMAIL>",
      "<EMAIL>"
    ]
  },
  {
    icon: "map-pin",
    title: "Address",
    items: [
      "123 Session Road",
      "Baguio City, 2600 Philippines"
    ]
  },
  {
    icon: "clock",
    title: "Office Hours",
    items: [
      "Monday - Friday: 8:00 AM - 5:00 PM",
      "Saturday: 8:00 AM - 12:00 PM"
    ]
  }
];

// Define social media links
const socialLinks = [
  {
    name: "Facebook",
    icon: "facebook",
    url: "#"
  },
  {
    name: "Instagram",
    icon: "instagram",
    url: "#"
  },
  {
    name: "Twitter",
    icon: "twitter",
    url: "#"
  },
  {
    name: "LinkedIn",
    icon: "linkedin",
    url: "#"
  },
  {
    name: "YouTube",
    icon: "youtube",
    url: "#"
  }
];

// Function to get icon SVG based on name
const getIconSvg = (iconName: string) => {
  switch(iconName) {
    case 'phone':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>`;
    case 'mail':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg>`;
    case 'map-pin':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path><circle cx="12" cy="10" r="3"></circle></svg>`;
    case 'clock':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>`;
    case 'facebook':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>`;
    case 'instagram':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line></svg>`;
    case 'twitter':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>`;
    case 'linkedin':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg>`;
    case 'youtube':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17"></path><path d="m10 15 5-3-5-3z"></path></svg>`;
    default:
      return `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" x2="12" y1="8" y2="16"></line><line x1="8" x2="16" y1="12" y2="12"></line></svg>`;
  }
};
---

<div class="bg-card border border-border/50 rounded-xl p-6 md:p-8 shadow-sm overflow-hidden relative h-full">
  <!-- Decorative elements -->
  <div class="absolute top-0 left-0 w-40 h-40 bg-primary/5 rounded-full blur-xl opacity-70 -translate-y-1/2 -translate-x-1/2"></div>
  
  <div class="relative z-10">
    <div class="info-header mb-8">
      <h3 class="text-2xl font-bold mb-2">Contact Information</h3>
      <p class="text-muted-foreground">Reach out to us through any of these channels</p>
    </div>
    
    <div class="space-y-6 info-items">
      {contactInfo.map((item, index) => (
        <div class="info-item flex items-start gap-4" data-index={index}>
          <div class="flex-shrink-0 w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary info-icon">
            <Fragment set:html={getIconSvg(item.icon)} />
          </div>
          <div>
            <h4 class="font-bold text-base info-title">{item.title}</h4>
            <div class="mt-1 space-y-1">
              {item.items.map((text) => (
                <p class="text-muted-foreground info-text">{text}</p>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
    
    <div class="mt-10 info-social">
      <h4 class="font-bold mb-4">Connect With Us</h4>
      <div class="flex flex-wrap gap-3">
        {socialLinks.map((link, index) => (
          <a 
            href={link.url} 
            class="social-link w-10 h-10 rounded-full bg-muted flex items-center justify-center text-muted-foreground hover:bg-primary hover:text-primary-foreground transition-colors duration-300 group"
            data-index={index}
            aria-label={link.name}
          >
            <span class="transform scale-100 group-hover:scale-110 transition-transform duration-300">
              <Fragment set:html={getIconSvg(link.icon)} />
            </span>
            <span class="sr-only">{link.name}</span>
          </a>
        ))}
      </div>
    </div>
    
    <!-- Quick response badge -->
    <div class="mt-10 info-badge">
      <div class="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 text-primary rounded-full text-sm">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="20 6 9 17 4 12"></polyline>
        </svg>
        <span>Quick Response Guaranteed</span>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Animate info elements on scroll
    const infoHeader = document.querySelector('.info-header');
    const infoItems = document.querySelectorAll('.info-item');
    const infoSocial = document.querySelector('.info-social');
    const infoBadge = document.querySelector('.info-badge');
    
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          if (entry.target === infoHeader) {
            infoHeader.classList.add('header-visible');
          }
          
          if (entry.target === infoSocial) {
            infoSocial.classList.add('social-visible');
            
            // Animate social links with staggered delay
            const socialLinks = document.querySelectorAll('.social-link');
            socialLinks.forEach((link, i) => {
              setTimeout(() => {
                link.classList.add('link-visible');
              }, 100 * i);
            });
          }
          
          if (entry.target === infoBadge) {
            infoBadge.classList.add('badge-visible');
          }
          
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    if (infoHeader) observer.observe(infoHeader);
    if (infoSocial) observer.observe(infoSocial);
    if (infoBadge) observer.observe(infoBadge);
    
    // Staggered animation for info items
    infoItems.forEach((item, index) => {
      const itemObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setTimeout(() => {
              item.classList.add('item-visible');
              
              // Animate item elements
              const infoIcon = item.querySelector('.info-icon');
              const infoTitle = item.querySelector('.info-title');
              const infoTexts = item.querySelectorAll('.info-text');
              
              if (infoIcon) {
                setTimeout(() => {
                  infoIcon.classList.add('icon-visible');
                }, 100);
              }
              
              if (infoTitle) {
                setTimeout(() => {
                  infoTitle.classList.add('title-visible');
                }, 200);
              }
              
              infoTexts.forEach((text, i) => {
                setTimeout(() => {
                  text.classList.add('text-visible');
                }, 300 + (i * 100));
              });
            }, 200 * index);
            
            itemObserver.unobserve(item);
          }
        });
      }, observerOptions);
      
      itemObserver.observe(item);
    });
  });
</script>

<style>
  /* Header animations */
  .info-header {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }
  
  .info-header.header-visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Info item animations */
  .info-item {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
  }
  
  .info-item.item-visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Icon animations */
  .info-icon {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.3s ease, transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  }
  
  .info-icon.icon-visible {
    opacity: 1;
    transform: scale(1);
  }
  
  /* Title animations */
  .info-title {
    opacity: 0;
    transform: translateX(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
  }
  
  .info-title.title-visible {
    opacity: 1;
    transform: translateX(0);
  }
  
  /* Text animations */
  .info-text {
    opacity: 0;
    transform: translateX(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
  }
  
  .info-text.text-visible {
    opacity: 1;
    transform: translateX(0);
  }
  
  /* Social section animations */
  .info-social {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
  }
  
  .info-social.social-visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Social link animations */
  .social-link {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease, 
                background-color 0.3s ease, color 0.3s ease;
  }
  
  .social-link.link-visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Badge animations */
  .info-badge {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
  }
  
  .info-badge.badge-visible {
    opacity: 1;
    transform: translateY(0);
  }
</style>
