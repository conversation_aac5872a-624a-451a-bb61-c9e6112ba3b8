---
import { buttonVariants } from "@/components/ui/button";

// Define form fields for animation
const formFields = [
  {
    id: "name",
    label: "Full Name",
    type: "text",
    placeholder: "Enter your full name",
    required: true
  },
  {
    id: "email",
    label: "Email Address",
    type: "email",
    placeholder: "Enter your email address",
    required: true
  },
  {
    id: "phone",
    label: "Phone Number",
    type: "tel",
    placeholder: "Enter your phone number",
    required: false
  },
  {
    id: "inquiry-type",
    label: "Inquiry Type",
    type: "select",
    required: true,
    options: [
      { value: "", label: "Select an inquiry type" },
      { value: "admission", label: "Admission Information" },
      { value: "program", label: "Program Inquiry" },
      { value: "financial", label: "Financial Aid & Scholarships" },
      { value: "visit", label: "Campus Visit" },
      { value: "other", label: "Other" }
    ]
  },
  {
    id: "message",
    label: "Your Message",
    type: "textarea",
    placeholder: "Please provide details about your inquiry...",
    required: true,
    rows: 5
  }
];
---

<div class="bg-card border border-border/50 rounded-xl p-6 md:p-8 shadow-sm overflow-hidden relative">
  <!-- Decorative elements -->
  <div class="absolute top-0 right-0 w-40 h-40 bg-primary/5 rounded-full blur-xl opacity-70 -translate-y-1/2 translate-x-1/2"></div>
  
  <div class="relative z-10">
    <div class="form-header mb-8">
      <h3 class="text-2xl font-bold mb-2">Send Us a Message</h3>
      <p class="text-muted-foreground">We'll get back to you as soon as possible</p>
    </div>
    
    <form class="space-y-6 form-body">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Name field -->
        <div class="form-field" data-index="0">
          <label for="name" class="form-label block text-sm font-medium mb-2">
            {formFields[0].label} {formFields[0].required && <span class="text-primary">*</span>}
          </label>
          <div class="form-input-wrapper relative">
            <input
              type={formFields[0].type}
              id={formFields[0].id}
              placeholder={formFields[0].placeholder}
              required={formFields[0].required}
              class="form-input w-full rounded-md border border-input bg-background px-4 py-2.5 text-sm ring-offset-background placeholder:text-muted-foreground focus:border-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/30 focus-visible:ring-offset-2 transition-all duration-300"
            />
            <div class="form-input-focus absolute inset-0 border border-primary/50 rounded-md opacity-0 pointer-events-none transition-opacity duration-300"></div>
          </div>
        </div>
        
        <!-- Email field -->
        <div class="form-field" data-index="1">
          <label for="email" class="form-label block text-sm font-medium mb-2">
            {formFields[1].label} {formFields[1].required && <span class="text-primary">*</span>}
          </label>
          <div class="form-input-wrapper relative">
            <input
              type={formFields[1].type}
              id={formFields[1].id}
              placeholder={formFields[1].placeholder}
              required={formFields[1].required}
              class="form-input w-full rounded-md border border-input bg-background px-4 py-2.5 text-sm ring-offset-background placeholder:text-muted-foreground focus:border-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/30 focus-visible:ring-offset-2 transition-all duration-300"
            />
            <div class="form-input-focus absolute inset-0 border border-primary/50 rounded-md opacity-0 pointer-events-none transition-opacity duration-300"></div>
          </div>
        </div>
      </div>
      
      <!-- Phone field -->
      <div class="form-field" data-index="2">
        <label for="phone" class="form-label block text-sm font-medium mb-2">
          {formFields[2].label} {formFields[2].required && <span class="text-primary">*</span>}
        </label>
        <div class="form-input-wrapper relative">
          <input
            type={formFields[2].type}
            id={formFields[2].id}
            placeholder={formFields[2].placeholder}
            required={formFields[2].required}
            class="form-input w-full rounded-md border border-input bg-background px-4 py-2.5 text-sm ring-offset-background placeholder:text-muted-foreground focus:border-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/30 focus-visible:ring-offset-2 transition-all duration-300"
          />
          <div class="form-input-focus absolute inset-0 border border-primary/50 rounded-md opacity-0 pointer-events-none transition-opacity duration-300"></div>
        </div>
      </div>
      
      <!-- Inquiry type field -->
      <div class="form-field" data-index="3">
        <label for="inquiry-type" class="form-label block text-sm font-medium mb-2">
          {formFields[3].label} {formFields[3].required && <span class="text-primary">*</span>}
        </label>
        <div class="form-input-wrapper relative">
          <select
            id={formFields[3].id}
            required={formFields[3].required}
            class="form-input w-full rounded-md border border-input bg-background px-4 py-2.5 text-sm ring-offset-background focus:border-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/30 focus-visible:ring-offset-2 transition-all duration-300"
          >
            {formFields[3].options.map(option => (
              <option value={option.value}>{option.label}</option>
            ))}
          </select>
          <div class="form-input-focus absolute inset-0 border border-primary/50 rounded-md opacity-0 pointer-events-none transition-opacity duration-300"></div>
          <div class="absolute right-4 top-1/2 -translate-y-1/2 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground">
              <path d="m6 9 6 6 6-6"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <!-- Message field -->
      <div class="form-field" data-index="4">
        <label for="message" class="form-label block text-sm font-medium mb-2">
          {formFields[4].label} {formFields[4].required && <span class="text-primary">*</span>}
        </label>
        <div class="form-input-wrapper relative">
          <textarea
            id={formFields[4].id}
            placeholder={formFields[4].placeholder}
            required={formFields[4].required}
            rows={formFields[4].rows}
            class="form-input w-full rounded-md border border-input bg-background px-4 py-2.5 text-sm ring-offset-background placeholder:text-muted-foreground focus:border-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/30 focus-visible:ring-offset-2 transition-all duration-300 resize-none"
          ></textarea>
          <div class="form-input-focus absolute inset-0 border border-primary/50 rounded-md opacity-0 pointer-events-none transition-opacity duration-300"></div>
        </div>
      </div>
      
      <!-- Submit button with loading state -->
      <div class="form-submit" data-index="5">
        <button 
          type="submit" 
          class={buttonVariants({ 
            size: "lg", 
            className: "w-full md:w-auto relative overflow-hidden group" 
          })}
        >
          <span class="relative z-10 flex items-center">
            <span>Send Message</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2 transform translate-x-0 group-hover:translate-x-1 transition-transform duration-300">
              <path d="M5 12h14"></path>
              <path d="m12 5 7 7-7 7"></path>
            </svg>
          </span>
          <span class="absolute inset-0 w-full h-full bg-white/10 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-500 ease-out"></span>
        </button>
        
        <div class="mt-4 text-sm text-muted-foreground">
          <span class="text-primary">*</span> Required fields
        </div>
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Animate form fields on scroll
    const formHeader = document.querySelector('.form-header');
    const formFields = document.querySelectorAll('.form-field');
    const formSubmit = document.querySelector('.form-submit');
    
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          if (entry.target === formHeader) {
            formHeader.classList.add('header-visible');
          }
          
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    if (formHeader) {
      observer.observe(formHeader);
    }
    
    // Staggered animation for form fields
    formFields.forEach((field, index) => {
      const fieldObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setTimeout(() => {
              field.classList.add('field-visible');
            }, 100 * index);
            
            fieldObserver.unobserve(field);
          }
        });
      }, observerOptions);
      
      fieldObserver.observe(field);
    });
    
    // Animation for submit button
    if (formSubmit) {
      const submitObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setTimeout(() => {
              formSubmit.classList.add('submit-visible');
            }, 100 * formFields.length);
            
            submitObserver.unobserve(formSubmit);
          }
        });
      }, observerOptions);
      
      submitObserver.observe(formSubmit);
    }
    
    // Focus and blur effects for inputs
    const formInputs = document.querySelectorAll('.form-input');
    formInputs.forEach(input => {
      input.addEventListener('focus', () => {
        const wrapper = input.closest('.form-input-wrapper');
        const focusElement = wrapper.querySelector('.form-input-focus');
        focusElement.style.opacity = '1';
      });
      
      input.addEventListener('blur', () => {
        const wrapper = input.closest('.form-input-wrapper');
        const focusElement = wrapper.querySelector('.form-input-focus');
        focusElement.style.opacity = '0';
      });
    });
    
    // Form submission with loading state
    const form = document.querySelector('form');
    if (form) {
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        
        const submitButton = form.querySelector('button[type="submit"]');
        const originalContent = submitButton.innerHTML;
        
        // Show loading state
        submitButton.innerHTML = `
          <span class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-primary-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Sending...
          </span>
        `;
        submitButton.disabled = true;
        
        // Simulate form submission
        setTimeout(() => {
          // Reset form
          form.reset();
          
          // Show success state
          submitButton.innerHTML = `
            <span class="flex items-center">
              <svg class="mr-2 h-4 w-4 text-primary-foreground" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
              Message Sent!
            </span>
          `;
          
          // Reset button after 2 seconds
          setTimeout(() => {
            submitButton.innerHTML = originalContent;
            submitButton.disabled = false;
          }, 2000);
        }, 1500);
      });
    }
  });
</script>

<style>
  /* Form header animations */
  .form-header {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }
  
  .form-header.header-visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Form field animations */
  .form-field {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
  }
  
  .form-field.field-visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Form submit animations */
  .form-submit {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
  }
  
  .form-submit.submit-visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Input focus animations */
  .form-input:focus {
    border-color: var(--primary);
  }
  
  /* Label animations */
  .form-label {
    transition: color 0.3s ease;
  }
  
  .form-input:focus + .form-input-focus + .form-label {
    color: var(--primary);
  }
</style>
