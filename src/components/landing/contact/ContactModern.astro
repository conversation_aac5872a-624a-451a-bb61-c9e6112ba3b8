---
import { buttonVariants } from "@/components/ui/button";
import ContactForm from "./ContactForm.astro";
import ContactInfo from "./ContactInfo.astro";
import ContactMap from "./ContactMap.astro";

const faqData = [
  {
    question: "What are the admission requirements for freshmen?",
    answer: `
      <p>For incoming freshmen in the Philippines, please prepare the following:</p>
      <ul class="list-disc list-inside mt-2 space-y-1">
        <li>Original and photocopy of your Grade 12 Report Card (Form 138).</li>
        <li>Certificate of Good Moral Character.</li>
        <li>Original and photocopy of your PSA Birth Certificate.</li>
        <li>Two (2) recent 2x2 ID pictures with a white background.</li>
      </ul>
      <p class="mt-2">Specific requirements may vary per program. We recommend checking the page of the program you are interested in.</p>
    `
  },
  {
    question: "When is the application period and entrance exam schedule?",
    answer: `
      <p>The application period for the <strong>2025-2026</strong> academic year runs from <strong>August 1, 2025, to December 15, 2025</strong>.</p>
      <p class="mt-2">The College Entrance Test (CET) is scheduled for <strong>January 11, 18, and 25, 2026</strong>. Applicants can select their preferred date upon submission of their application.</p>
    `
  },
  {
    question: "What are the estimated tuition fees and payment schemes?",
    answer: `
      <p>The estimated tuition fee per semester ranges from <strong>₱30,000 to ₱50,000</strong>, depending on the program and number of units.</p>
      <p class="mt-2">We offer flexible payment options, including:</p>
      <ul class="list-disc list-inside mt-2 space-y-1">
        <li>Full payment (with an early bird discount).</li>
        <li>Installment plans (up to four payments per semester).</li>
        <li>Payments via bank transfer, credit card, or GCash.</li>
      </ul>
    `
  },
  {
    question: "Do you accept transferees and second-degree applicants?",
    answer: `
      <p>Yes, we welcome transferees and second-degree course applicants. Key requirements include your Transcript of Records (TOR) for evaluation, Honorable Dismissal, and a Certificate of Good Moral Character from your previous institution. The crediting of subjects is subject to evaluation by the college dean. Please contact the admissions office for a detailed checklist.</p>
    `
  }
];
---

<section id="contact" class="py-16 sm:py-20 lg:py-32 relative overflow-hidden">
    <!-- Modern Background Elements -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-secondary/3 pointer-events-none"></div>
    
    <!-- Animated Background Particles -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-20 left-10 w-32 h-32 bg-primary/8 rounded-full blur-3xl animate-float opacity-60"></div>
        <div class="absolute top-60 right-20 w-24 h-24 bg-secondary/10 rounded-full blur-2xl animate-float-delayed opacity-40"></div>
        <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-primary/5 rounded-full blur-3xl animate-float-slow opacity-30"></div>
        <div class="absolute bottom-60 right-10 w-28 h-28 bg-secondary/8 rounded-full blur-2xl animate-float opacity-50"></div>
    </div>
    
    <!-- Grid Pattern Overlay -->
    <div class="absolute inset-0 opacity-[0.02] pointer-events-none" style="background-image: radial-gradient(circle at 1px 1px, var(--foreground) 1px, transparent 0); background-size: 20px 20px;"></div>
    
    <div class="container relative z-10 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <!-- Enhanced Header -->
        <div class="text-center mb-16 sm:mb-20 lg:mb-24">
            <div class="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium mb-6 contact-badge">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                Contact Us
            </div>
            
            <h2 class="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent contact-title">
                Let's Start Your <span class="text-primary">Journey</span>
            </h2>
            
            <p class="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed contact-description">
                Ready to take the next step? We're here to answer your questions and guide you through your educational journey.
            </p>
        </div>
        
        <!-- Main Contact Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12 mb-16 sm:mb-20 lg:mb-24">
            <!-- Contact Info Card -->
            <div class="lg:col-span-1 contact-info-container">
                <div class="contact-card group h-full">
                    <div class="absolute inset-0 rounded-2xl bg-gradient-to-br from-primary/10 via-transparent to-secondary/10 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                    <div class="relative bg-card border border-border/40 rounded-2xl p-6 sm:p-8 h-full group-hover:border-primary/30 group-hover:shadow-2xl group-hover:shadow-primary/10 transition-all duration-500 transform group-hover:-translate-y-1">
                        <ContactInfo />
                    </div>
                </div>
            </div>
            
            <!-- Contact Form Card -->
            <div class="lg:col-span-2 contact-form-container">
                <div class="contact-card group h-full">
                    <div class="absolute inset-0 rounded-2xl bg-gradient-to-br from-primary/10 via-transparent to-secondary/10 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                    <div class="relative bg-card border border-border/40 rounded-2xl p-6 sm:p-8 h-full group-hover:border-primary/30 group-hover:shadow-2xl group-hover:shadow-primary/10 transition-all duration-500 transform group-hover:-translate-y-1">
                        <ContactForm />
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Map Section -->
        <div class="mb-16 sm:mb-20 lg:mb-24 contact-map-container">
            <div class="contact-card group">
                <div class="absolute inset-0 rounded-2xl bg-gradient-to-br from-primary/10 via-transparent to-secondary/10 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                <div class="relative bg-card border border-border/40 rounded-2xl overflow-hidden group-hover:border-primary/30 group-hover:shadow-2xl group-hover:shadow-primary/10 transition-all duration-500 transform group-hover:-translate-y-1">
                    <ContactMap />
                </div>
            </div>
        </div>
        
        <!-- Enhanced FAQ Section -->
        <div class="faq-section">
            <!-- FAQ Header -->
            <div class="text-center mb-12 sm:mb-16">
                <div class="inline-flex items-center gap-2 px-4 py-2 bg-secondary/60 text-secondary-foreground rounded-full text-sm font-medium mb-6 faq-badge">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                        <path d="M12 17h.01"></path>
                    </svg>
                    Frequently Asked Questions
                </div>
                
                <h3 class="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent faq-title">
                    Got Questions?
                </h3>
                
                <p class="text-lg text-muted-foreground max-w-2xl mx-auto faq-description">
                    Find quick answers to common questions about our admission process, requirements, and schedules.
                </p>
            </div>
            
            <!-- FAQ Accordion -->
            <div class="max-w-4xl mx-auto">
                <div class="grid grid-cols-1 gap-4 faq-container">
                    {faqData.map((faq, index) => (
                        <div class="faq-item group" data-index={index}>
                            <div class="bg-card border border-border/40 rounded-xl overflow-hidden group-hover:border-primary/30 group-hover:shadow-lg transition-all duration-300">
                                <button class="faq-trigger w-full flex items-center justify-between p-6 text-left font-medium hover:bg-secondary/30 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2">
                                    <span class="text-base sm:text-lg font-semibold">{faq.question}</span>
                                    <div class="flex-shrink-0 ml-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="faq-icon transition-transform duration-300 text-primary">
                                            <path d="m6 9 6 6 6-6"></path>
                                        </svg>
                                    </div>
                                </button>
                                <div class="faq-content overflow-hidden transition-all duration-300 ease-in-out max-h-0">
                                    <div class="p-6 pt-0 text-muted-foreground border-t border-border/30">
                                        <div class="leading-relaxed space-y-3" set:html={faq.answer} />
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
        
        <!-- Call to Action Section -->
        
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        // Enhanced entrance animations
        const animateElements = () => {
            // Animate contact badge
            const contactBadge = document.querySelector('.contact-badge');
            if (contactBadge) {
                contactBadge.style.opacity = '0';
                contactBadge.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    contactBadge.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    contactBadge.style.opacity = '1';
                    contactBadge.style.transform = 'translateY(0)';
                }, 100);
            }
            
            // Animate title
            const contactTitle = document.querySelector('.contact-title');
            if (contactTitle) {
                contactTitle.style.opacity = '0';
                contactTitle.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    contactTitle.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                    contactTitle.style.opacity = '1';
                    contactTitle.style.transform = 'translateY(0)';
                }, 200);
            }
            
            // Animate description
            const contactDescription = document.querySelector('.contact-description');
            if (contactDescription) {
                contactDescription.style.opacity = '0';
                contactDescription.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    contactDescription.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    contactDescription.style.opacity = '1';
                    contactDescription.style.transform = 'translateY(0)';
                }, 400);
            }
        };
        
        // Enhanced card hover effects
        const setupCardEffects = () => {
            const cards = document.querySelectorAll('.contact-card');
            cards.forEach((card, index) => {
                // Initial animation
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 600 + (index * 200));
                
                // Mouse hover effect
                card.addEventListener('mousemove', (e) => {
                    const rect = card.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    
                    const rotateX = (y - centerY) / 30;
                    const rotateY = (centerX - x) / 30;
                    
                    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
                    card.style.transition = 'transform 0.1s ease';
                });
                
                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
                    card.style.transition = 'transform 0.5s ease';
                });
            });
        };
        
        // Enhanced FAQ functionality
        const setupFAQ = () => {
            const faqItems = document.querySelectorAll('.faq-item');
            
            // Animate FAQ items on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100);
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);
            
            faqItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(item);
            });
            
            // FAQ accordion functionality
            const faqTriggers = document.querySelectorAll('.faq-trigger');
            faqTriggers.forEach(trigger => {
                trigger.addEventListener('click', () => {
                    const content = trigger.nextElementSibling;
                    const icon = trigger.querySelector('.faq-icon');
                    const isOpen = content.classList.contains('active');
                    
                    // Close all items
                    document.querySelectorAll('.faq-content').forEach(item => {
                        item.classList.remove('active');
                        item.style.maxHeight = '0';
                    });
                    
                    document.querySelectorAll('.faq-icon').forEach(icon => {
                        icon.style.transform = 'rotate(0)';
                    });
                    
                    // Open clicked item if it wasn't open
                    if (!isOpen) {
                        content.classList.add('active');
                        content.style.maxHeight = content.scrollHeight + 'px';
                        icon.style.transform = 'rotate(180deg)';
                        
                        // Smooth scroll to FAQ item if needed
                        setTimeout(() => {
                            const rect = trigger.getBoundingClientRect();
                            if (rect.top < 100) {
                                trigger.scrollIntoView({ 
                                    behavior: 'smooth', 
                                    block: 'start',
                                    inline: 'nearest'
                                });
                            }
                        }, 300);
                    }
                });
            });
        };
        
        // Initialize all animations and effects
        animateElements();
        setupCardEffects();
        setupFAQ();
        
        // Animate other sections on scroll
        const observeOtherElements = () => {
            const elementsToObserve = [
                '.faq-badge',
                '.faq-title', 
                '.faq-description',
                '.cta-section'
            ];
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 });
            
            elementsToObserve.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(20px)';
                    element.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                    observer.observe(element);
                }
            });
        };
        
        observeOtherElements();
    });
</script>

<style>
    /* Enhanced animations */
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-10px) rotate(1deg); }
        66% { transform: translateY(5px) rotate(-1deg); }
    }
    
    @keyframes float-delayed {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(8px) rotate(-1deg); }
        66% { transform: translateY(-5px) rotate(1deg); }
    }
    
    @keyframes float-slow {
        0%, 100% { transform: translateY(0px) scale(1); }
        50% { transform: translateY(-15px) scale(1.05); }
    }
    
    .animate-float {
        animation: float 20s ease-in-out infinite;
    }
    
    .animate-float-delayed {
        animation: float-delayed 25s ease-in-out infinite;
        animation-delay: 2s;
    }
    
    .animate-float-slow {
        animation: float-slow 30s ease-in-out infinite;
        animation-delay: 5s;
    }
    
    /* Background gradient text */
    .bg-clip-text {
        -webkit-background-clip: text;
        background-clip: text;
    }
    
    /* Enhanced card styling */
    .contact-card {
        position: relative;
        will-change: transform;
        transform-style: preserve-3d;
    }
    
    /* FAQ content animations */
    .faq-content {
        transition: max-height 0.3s ease-in-out;
    }
    
    .faq-content.active {
        transition: max-height 0.5s ease-in-out;
    }
    
    /* Improved focus states */
    button:focus-visible {
        outline: 2px solid hsl(var(--primary));
        outline-offset: 2px;
    }
    
    /* Enhanced hover effects */
    .contact-card:hover {
        z-index: 10;
    }
    
    /* Mobile optimizations */
    @media (max-width: 640px) {
        .container {
            padding-left: 1rem;
            padding-right: 1rem;
        }
        
        .contact-card {
            transform: none !important;
        }
        
        .contact-card:hover {
            transform: translateY(-2px) !important;
        }
    }
    
    /* Smooth scrolling */
    html {
        scroll-behavior: smooth;
    }
    
    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .animate-float,
        .animate-float-delayed,
        .animate-float-slow {
            animation: none;
        }
        
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
        
        .contact-card {
            transform: none !important;
        }
    }
    
    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .bg-gradient-to-br,
        .bg-gradient-to-r {
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }
    }
    
    /* Print styles */
    @media print {
        .absolute,
        .animate-float,
        .animate-float-delayed,
        .animate-float-slow {
            display: none;
        }
    }
</style>
