---
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { getSchoolSettings } from "@/lib/api";

// Fetch school settings
const schoolSettings = await getSchoolSettings();
const { 
  school_year, 
  school_year_string, 
  semester, 
  online_enrollment_enabled 
} = schoolSettings.data;
---

<section class="relative isolate overflow-hidden">
  <!-- Split layout container -->
  <div class="flex flex-col lg:flex-row lg:min-h-screen">
    <!-- Left content column - takes full width on mobile, 45% on desktop -->
    <div class="relative z-10 flex flex-col justify-center px-4 py-12 sm:px-6 lg:w-[45%] lg:py-0 lg:pl-8 lg:pr-0 xl:pl-16">
      <div class="mx-auto w-full max-w-xl lg:mx-0">
        <!-- Animated badge with gradient border - only shown when online enrollment is enabled -->
     
        
        <!-- Main heading with animated gradient text -->
        <div class="space-y-2">
          <div class="animate-slide-up overflow-hidden" style="--slide-delay: 100ms;">
            <h1 class="fancy-heading text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
              <span class="relative inline-block">
                <span class="animate-text-reveal" style="--reveal-delay: 300ms;">Transform</span>
              </span>
              <span class="relative ml-1 inline-block">
                <span class="animate-text-reveal" style="--reveal-delay: 500ms;">Your</span>
              </span>
              <span class="relative ml-1 inline-block">
                <span class="animate-text-reveal" style="--reveal-delay: 700ms;">Future</span>
              </span>
            </h1>
          </div>
          {online_enrollment_enabled && (
            <div class="animate-slide-up" style="--slide-delay: 0ms;">
              <div class="group relative mb-6 inline-flex items-center rounded-full border border-primary/30 bg-background/80 px-4 py-1.5 text-sm font-medium text-primary backdrop-blur-md transition-all duration-300 hover:border-primary/60">
                <div class="absolute -inset-[1px] -z-10 rounded-full bg-gradient-to-r from-primary/20 via-primary/0 to-primary/20 opacity-0 blur-md transition-all duration-500 group-hover:opacity-100"></div>
                <span class="mr-2 inline-block h-2 w-2 animate-pulse rounded-full bg-primary"></span>
                Now Accepting Applications - {school_year_string} {semester}
              </div>
            </div>
          )}
          <div class="animate-slide-up" style="--slide-delay: 200ms;">
            <div class="relative">
              <h2 class="gradient-text text-2xl font-bold sm:text-3xl md:text-4xl">
                Data Center College
              </h2>
              <div class="absolute -inset-1 -z-10 animate-pulse opacity-30 blur-xl">
                <div class="h-full w-full rounded-full bg-gradient-to-r from-primary via-primary/50 to-primary/20"></div>
              </div>
            </div>
            <p class="mt-1 text-lg font-medium text-foreground/80">Baguio City, Philippines</p>
          </div>
        </div>
        
        <!-- Description with animated reveal -->
        <div class="mt-6 animate-slide-up" style="--slide-delay: 300ms;">
          <p class="max-w-md text-muted-foreground">
            A premier institution for technology and business education, empowering students with the skills and knowledge needed for tomorrow's challenges.
          </p>
        </div>
        
        <!-- Interactive CTA buttons -->
        <div class="mt-8 flex flex-wrap gap-4 animate-slide-up" style="--slide-delay: 400ms;">
          <Button size="lg" className="group relative overflow-hidden bg-primary px-8 py-6 text-lg">
            <span class="relative z-10 flex items-center font-medium">
              Explore Programs
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1">
                <path d="M5 12h14"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
            </span>
            <span class="absolute inset-0 -z-10 bg-gradient-to-r from-primary to-primary/80 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></span>
          </Button>
          
          <div class="group relative">
            <Button variant="outline" size="lg" className="relative z-10 border-primary/30 px-8 py-6 text-lg backdrop-blur-sm">
              <span class="font-medium">Apply Now</span>
            </Button>
            <div class="absolute -inset-[2px] -z-0 rounded-md bg-gradient-to-r from-primary/30 to-primary/10 opacity-0 blur-md transition-all duration-300 group-hover:opacity-100"></div>
          </div>
        </div>
        
        <!-- Stats with animated counting -->
        <div class="mt-12 grid grid-cols-3 gap-4 animate-slide-up" style="--slide-delay: 500ms;">
          <div class="relative overflow-hidden rounded-xl border border-border/40 bg-background/60 p-4 backdrop-blur-md transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/5">
            <div class="absolute -right-6 -top-6 h-12 w-12 rounded-full bg-primary/10"></div>
            <div class="relative">
              <div class="text-2xl font-bold count-up" data-target="30">0</div>
              <div class="text-xs text-muted-foreground">Years Experience</div>
            </div>
          </div>
          
          <div class="relative overflow-hidden rounded-xl border border-border/40 bg-background/60 p-4 backdrop-blur-md transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/5">
            <div class="absolute -right-6 -top-6 h-12 w-12 rounded-full bg-primary/10"></div>
            <div class="relative">
              <div class="text-2xl font-bold count-up" data-target="95">0</div>
              <div class="text-xs text-muted-foreground">Employment Rate</div>
            </div>
          </div>
          
          <div class="relative overflow-hidden rounded-xl border border-border/40 bg-background/60 p-4 backdrop-blur-md transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/5">
            <div class="absolute -right-6 -top-6 h-12 w-12 rounded-full bg-primary/10"></div>
            <div class="relative">
              <div class="text-2xl font-bold count-up" data-target="50">0</div>
              <div class="text-xs text-muted-foreground">Industry Partners</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Decorative elements -->
      <div class="pointer-events-none absolute bottom-0 left-0 right-0 hidden h-24 translate-y-1/2 lg:block">
        <div class="absolute inset-0 bg-gradient-to-r from-background via-background/80 to-transparent"></div>
      </div>
    </div>
    
    <!-- Right image column with effects - hidden on mobile, 55% on desktop -->
    <div class="relative hidden lg:block lg:w-[55%]">
      <!-- Image container with clip path -->
      <div class="absolute inset-0 clip-path-polygon">
        <!-- Main image with parallax effect -->
        <div class="parallax-container h-full w-full overflow-hidden">
          <img 
            src="/campus.webp" 
            alt="DCCP Campus" 
            class="h-full w-full scale-110 object-cover transition-transform duration-[2s] hover:scale-100"
            id="parallax-img"
          />
          
          <!-- Overlay gradients -->
          <div class="absolute inset-0 bg-gradient-to-t from-background/80 via-background/20 to-transparent opacity-80"></div>
          <div class="absolute inset-0 bg-gradient-to-r from-background/80 via-background/40 to-transparent"></div>
          
          <!-- Floating elements -->
          <div class="absolute left-[10%] top-[20%] h-24 w-24 rounded-full border border-primary/20 bg-background/10 backdrop-blur-md"></div>
          <div class="absolute bottom-[30%] right-[15%] h-32 w-32 rounded-full border border-primary/20 bg-background/10 backdrop-blur-md"></div>
          
          <!-- Animated accent lines -->
          <div class="absolute inset-0">
            <div class="absolute left-1/4 top-0 h-1/2 w-px animate-glow bg-gradient-to-b from-transparent via-primary/50 to-transparent"></div>
            <div class="absolute bottom-0 right-1/3 h-1/2 w-px animate-glow-delayed bg-gradient-to-b from-transparent via-primary/50 to-transparent"></div>
            <div class="absolute bottom-1/4 left-0 h-px w-1/3 animate-glow-delayed bg-gradient-to-r from-transparent via-primary/50 to-transparent"></div>
          </div>
        </div>
      </div>
    
      
      
      <div class="absolute right-12 top-12 z-10 max-w-xs animate-float-delayed">
        <div class="group relative overflow-hidden rounded-xl border border-border/40 bg-background/80 p-4 backdrop-blur-md transition-all duration-300 hover:-translate-y-1 hover:bg-background/90 hover:shadow-lg">
          <div class="flex items-center gap-3">
            <div class="rounded-full bg-primary/10 p-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-primary">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
              </svg>
            </div>
            <div>
              <h3 class="font-medium">Modern Campus</h3>
              <p class="text-xs text-muted-foreground">State-of-the-art facilities</p>
            </div>
          </div>
          <div class="absolute -inset-[1px] -z-10 rounded-xl bg-gradient-to-r from-primary/20 via-primary/0 to-primary/20 opacity-0 blur-md transition-all duration-500 group-hover:opacity-100"></div>
        </div>
      </div>
    </div>
    
    <!-- Mobile image (shown only on mobile) -->
    <div class="relative h-64 w-full lg:hidden">
      <div class="absolute inset-0">
        <img 
          src="/campus.png" 
          alt="DCCP Campus" 
          class="h-full w-full object-cover"
        />
        <div class="absolute inset-0 bg-gradient-to-t from-background via-background/60 to-background/30"></div>
      </div>
    </div>
  </div>
  
  <!-- Mouse scroll indicator -->
  <div class="absolute bottom-8 left-1/2 hidden -translate-x-1/2 animate-bounce md:block">
    <a href="#about" class="group flex flex-col items-center text-muted-foreground transition-colors hover:text-foreground">
      <span class="text-xs font-medium">Discover More</span>
      <div class="mt-2 h-8 w-5 rounded-full border border-muted-foreground/40 p-1 transition-colors duration-300 group-hover:border-foreground/60">
        <div class="h-1.5 w-1.5 animate-scroll-down rounded-full bg-muted-foreground/80 transition-colors duration-300 group-hover:bg-foreground"></div>
      </div>
    </a>
  </div>
</section>

<script>
  function restartAnimations() {
    // Re-trigger CSS animations
    const animatedElements = document.querySelectorAll('[class*="animate-"]');
    animatedElements.forEach((el) => {
      el.classList.remove(...Array.from(el.classList).filter(c => c.startsWith('animate-')));
      // Reading offsetWidth is a trick to force reflow
            void (el as HTMLElement).offsetWidth;
      el.classList.add(...Array.from(el.classList).filter(c => c.startsWith('animate-')));
    });

    // Re-run count-up animation
    const countUpObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const el = entry.target as HTMLElement;
          const target = parseInt(el.dataset.target || '0', 10);
          let current = 0;
          const increment = target / 100;
          const interval = setInterval(() => {
            current += increment;
            if (current >= target) {
              current = target;
              clearInterval(interval);
            }
            el.textContent = Math.ceil(current).toString();
          }, 20);
          observer.unobserve(el);
        }
      });
    }, { threshold: 0.5 });

    document.querySelectorAll('.count-up').forEach(el => {
        (el as HTMLElement).textContent = '0'; // Reset counter
        countUpObserver.observe(el);
    });
  }

  // Run on initial load
  document.addEventListener('astro:page-load', restartAnimations);
</script>

<style>
  /* Clip path for the image section */
  .clip-path-polygon {
    clip-path: polygon(15% 0%, 100% 0%, 100% 100%, 0% 100%);
  }
  
  /* Fancy gradient text */
  .gradient-text {
    background: linear-gradient(to right, hsl(var(--primary)), hsl(var(--primary) / 0.8));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
    position: relative;
  }
  
  /* Fancy heading with animated underline */
  .fancy-heading {
    position: relative;
  }
  
  .fancy-heading::after {
    content: '';
    position: absolute;
    bottom: 0.1em;
    left: 0;
    width: 100%;
    height: 0.1em;
    background: linear-gradient(to right, hsl(var(--primary) / 0.7), hsl(var(--primary) / 0.1));
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.5s ease;
  }
  
  .fancy-heading:hover::after {
    transform: scaleX(1);
    transform-origin: left;
  }
  
  /* Animation keyframes */
  @keyframes slide-up {
    0% {
      transform: translateY(30px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  @keyframes text-reveal {
    0% {
      transform: translateY(100%);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  @keyframes float-delayed {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  @keyframes glow {
    0%, 100% {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
  }
  
  @keyframes glow-delayed {
    0%, 100% {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
  }
  
  @keyframes scroll-down {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(10px);
    }
  }
  
  /* Apply animations */
  .animate-slide-up {
    animation: slide-up 0.8s ease forwards;
    animation-delay: var(--slide-delay, 0s);
    opacity: 0;
  }
  
  .animate-text-reveal {
    display: inline-block;
    overflow: hidden;
    transform: translateY(100%);
    animation: text-reveal 0.8s cubic-bezier(0.5, 0, 0.1, 1) forwards;
    animation-delay: var(--reveal-delay, 0s);
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-float-delayed {
    animation: float-delayed 7s ease-in-out 1s infinite;
  }
  
  .animate-glow {
    animation: glow 4s ease-in-out infinite;
  }
  
  .animate-glow-delayed {
    animation: glow-delayed 4s ease-in-out 2s infinite;
  }
  
  .animate-scroll-down {
    animation: scroll-down 1.5s ease-in-out infinite;
  }
  
  /* Ensure dark mode compatibility */
  :global(.dark) .bg-background\/80 {
    background-color: hsl(var(--background) / 0.8);
  }
  
  :global(.dark) .bg-background\/60 {
    background-color: hsl(var(--background) / 0.6);
  }
  
  :global(.dark) .from-background\/80 {
    --tw-gradient-from: hsl(var(--background) / 0.8);
  }
</style>

<script>
  // Parallax effect for the image
  document.addEventListener('DOMContentLoaded', () => {
    const parallaxImg = document.getElementById('parallax-img');
    
    if (parallaxImg) {
      window.addEventListener('mousemove', (e) => {
        const mouseX = e.clientX / window.innerWidth;
        const mouseY = e.clientY / window.innerHeight;
        
        const moveX = 20 * (0.5 - mouseX);
        const moveY = 20 * (0.5 - mouseY);
        
        parallaxImg.style.transform = `translate(${moveX}px, ${moveY}px) scale(1.1)`;
      });
    }
    
    // Animated counting for stats
    const countElements = document.querySelectorAll('.count-up');
    
    countElements.forEach(element => {
      const targetAttribute = element.getAttribute('data-target');
      if (targetAttribute === null) {
        console.warn('Element is missing data-target attribute:', element);
        return; // Skip this element if attribute is missing
      }
      const target = parseInt(targetAttribute, 10); // Added radix 10
      if (isNaN(target)) {
        console.warn('Invalid data-target attribute:', element);
        return; // Skip if target is not a number
      }

      const duration = 2000; // ms
      const frameDuration = 1000 / 60; // 60fps
      const totalFrames = Math.round(duration / frameDuration);
      const countIncrement = target / totalFrames;
      
      let currentCount = 0;
      const counter = setInterval(() => {
        currentCount += countIncrement;
        
        if (currentCount >= target) {
          element.textContent = target.toString(); // Convert number to string
          clearInterval(counter);
        } else {
          element.textContent = Math.floor(currentCount).toString(); // Convert number to string
        }
      }, frameDuration);
    });
  });
</script>
