---
import ProgramsVisualHeader from "./ProgramsVisualHeader.astro";
import ProgramsVisualCard from "./ProgramsVisualCard.astro";

// Updated program data based on dccp.edu.ph/programs
const programs = [
    // College Programs
    {
        id: "bsit",
        category: "College",
        title: "Information Technology",
        description: "Develop expertise in programming, systems analysis, and digital innovation for the modern tech landscape.",
        duration: "4 Years",
        format: "On Campus",
        degree: "Bachelor of Science",
        image: "images/bsit-image.png",
        color: "blue",
        specializations: [
            { name: "Web Development", icon: "code" },
            { name: "Cloud Computing", icon: "cloud" },
            { name: "Cybersecurity", icon: "shield" },
            { name: "Data Science", icon: "database" },
        ],
        features: [
            "Industry-aligned curriculum",
            "State-of-the-art computer labs",
            "Internship opportunities with tech companies",
            "Certification preparation programs",
        ],
        isPopular: true,
        learnMoreLink: "/programs/college/information-technology",
    },
    {
        id: "bsba",
        category: "College",
        title: "Business Administration",
        description: "Master principles of management, marketing, finance, and entrepreneurship in today's global economy.",
        duration: "4 Years",
        format: "On Campus",
        degree: "Bachelor of Science",
        image: "/images/programs/college-business.jpg",
        color: "amber",
        specializations: [
            { name: "Financial Management", icon: "banknote" },
            { name: "Marketing Strategy", icon: "presentation" },
            { name: "Human Resources", icon: "users" },
            { name: "Entrepreneurship", icon: "lightbulb" },
        ],
        features: [
            "Business simulation projects",
            "Industry partnerships and mentorship",
            "Leadership development program",
            "Global business perspective",
        ],
        learnMoreLink: "/programs/college/business-administration",
    },
    {
        id: "bshm",
        category: "College",
        title: "Hospitality Management",
        description: "Prepare for leadership in hospitality with comprehensive practical and theoretical knowledge.",
        duration: "4 Years",
        format: "On Campus",
        degree: "Bachelor of Science",
        image: "images/hm.jpg",
        color: "green",
        specializations: [
            { name: "Hotel Management", icon: "building" },
            { name: "Event Planning", icon: "calendar" },
            { name: "Tourism Management", icon: "map" },
            { name: "Culinary Arts", icon: "utensils" },
        ],
        features: [
            "Training restaurant and kitchen facilities",
            "Industry internships and placements",
            "Event management experience",
            "International hospitality standards",
        ],
        isNew: true,
        learnMoreLink: "/programs/college/hospitality-management",
    },
    // K-12 Senior High School Programs
    {
        id: "k12-stem",
        category: "K-12 Senior High",
        title: "STEM",
        description: "Science, Technology, Engineering, and Mathematics track preparing students for technological advancement.",
        duration: "2 Years",
        format: "On Campus",
        degree: "Senior High School Strand",
        image: "/images/programs/k12-stem.jpg",
        color: "sky",
        specializations: [
            { name: "Advanced Mathematics", icon: "calculator" },
            { name: "Physical Sciences", icon: "beaker" },
            { name: "Engineering Tech", icon: "cpu" },
            { name: "Programming", icon: "code-xml" },
        ],
        features: [
            "Inquiry-based learning approach",
            "Hands-on science projects",
            "University STEM preparation",
        ],
        learnMoreLink: "/programs/k12/stem",
    },
    {
        id: "k12-abm",
        category: "K-12 Senior High",
        title: "ABM",
        description: "Accountancy, Business, and Management track developing future entrepreneurs and business leaders.",
        duration: "2 Years",
        format: "On Campus",
        degree: "Senior High School Strand",
        image: "/images/programs/k12-abm.jpg",
        color: "orange",
        specializations: [
            { name: "Business Mathematics", icon: "percent" },
            { name: "Accountancy Basics", icon: "book-open" },
            { name: "Business Ethics", icon: "scale" },
            { name: "Economics", icon: "line-chart" },
        ],
        features: [
            "Business principles foundation",
            "Case studies and simulations",
            "Entrepreneurial skills development",
        ],
        learnMoreLink: "/programs/k12/abm",
    },
    {
        id: "k12-humss",
        category: "K-12 Senior High",
        title: "HUMSS",
        description: "Humanities and Social Sciences track exploring human society, culture, and critical thinking skills.",
        duration: "2 Years",
        format: "On Campus",
        degree: "Senior High School Strand",
        image: "/images/programs/k12-humss.jpg",
        color: "rose",
        specializations: [
            { name: "Creative Writing", icon: "pencil-ruler" },
            { name: "Social Sciences", icon: "users-round" },
            { name: "Political Science", icon: "landmark" },
            { name: "Psychology", icon: "brain" },
        ],
        features: [
            "Communication skills development",
            "Understanding of societal issues",
            "Critical analysis encouragement",
        ],
        learnMoreLink: "/programs/k12/humss",
    },
    {
        id: "k12-he",
        category: "K-12 Senior High",
        title: "Home Economics",
        description: "Focusing on livelihood skills, family and consumer sciences for practical life application.",
        duration: "2 Years",
        format: "On Campus",
        degree: "Senior High School Strand",
        image: "/images/programs/k12-he.jpg",
        color: "lime",
        specializations: [
            { name: "Food & Nutrition", icon: "chef-hat" },
            { name: "Culinary Arts", icon: "utensils-crossed" },
            { name: "Home Management", icon: "home" },
            { name: "Food Safety", icon: "shield-check" },
        ],
        features: [
            "Practical cooking and baking",
            "Nutrition and wellness principles",
            "Household resource management",
        ],
        learnMoreLink: "/programs/k12/he",
    },
    {
        id: "k12-ict",
        category: "K-12 Senior High",
        title: "ICT",
        description: "Information and Communications Technology building foundational skills in digital tools and technologies.",
        duration: "2 Years",
        format: "On Campus",
        degree: "Senior High School Strand",
        image: "/images/programs/k12-ict.jpg",
        color: "cyan",
        specializations: [
            { name: "Programming", icon: "keyboard" },
            { name: "Digital Arts", icon: "image" },
            { name: "Web Development", icon: "layout-grid" },
            { name: "Network Systems", icon: "network" },
        ],
        features: [
            "Introduction to coding languages",
            "Digital content creation",
            "IT infrastructure understanding",
        ],
        learnMoreLink: "/programs/k12/ict",
    },
];

// Group programs by category
const collegePrograms = programs.filter(p => p.category === "College");
const k12Programs = programs.filter(p => p.category === "K-12 Senior High");
---

<section id="programs" class="py-16 sm:py-20 lg:py-32 relative overflow-hidden">
    <!-- Advanced Background Elements -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-secondary/3 pointer-events-none"></div>
    
    <!-- Animated Background Particles -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-10 left-10 w-32 h-32 bg-primary/10 rounded-full blur-3xl animate-float opacity-60"></div>
        <div class="absolute top-40 right-20 w-24 h-24 bg-secondary/10 rounded-full blur-2xl animate-float-delayed opacity-40"></div>
        <div class="absolute bottom-20 left-1/4 w-40 h-40 bg-primary/5 rounded-full blur-3xl animate-float-slow opacity-30"></div>
        <div class="absolute bottom-40 right-10 w-28 h-28 bg-secondary/8 rounded-full blur-2xl animate-float opacity-50"></div>
    </div>
    
    <!-- Grid Pattern Overlay -->
    <div class="absolute inset-0 opacity-[0.02] pointer-events-none" style="background-image: radial-gradient(circle at 1px 1px, var(--foreground) 1px, transparent 0); background-size: 20px 20px;"></div>
    
    <div class="container relative z-10 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <!-- Enhanced Header -->
        <div class="text-center mb-16 sm:mb-20 lg:mb-24">
            <ProgramsVisualHeader />
        </div>
        
        <!-- College Programs Section -->
        <div class="mb-20 sm:mb-24 lg:mb-32">
            <!-- Section Header -->
            <div class="text-center mb-12 sm:mb-16">
                <div class="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M22 10v6M2 10l10-5 10 5-10 5z"></path>
                        <path d="M6 12v5c3 3 9 3 12 0v-5"></path>
                    </svg>
                    College Programs
                </div>
                <h3 class="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                    Build Your Future
                </h3>
                <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                    Comprehensive degree programs designed to prepare you for success in your chosen field
                </p>
            </div>
            
            <!-- College Programs Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
                {collegePrograms.map((program, index) => (
                    <ProgramsVisualCard program={program} index={index} />
                ))}
            </div>
        </div>
        
        <!-- K-12 Senior High Programs Section -->
        <div class="mb-20 sm:mb-24 lg:mb-32">
            <!-- Section Header -->
            <div class="text-center mb-12 sm:mb-16">
                <div class="inline-flex items-center gap-2 px-4 py-2 bg-secondary/60 text-secondary-foreground rounded-full text-sm font-medium mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M14 22v-4a2 2 0 1 0-4 0v4"></path>
                        <path d="m18 10 4 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8l4-2"></path>
                        <path d="M18 5v17"></path>
                        <path d="m4 6 8-4 8 4"></path>
                        <path d="M6 5v17"></path>
                    </svg>
                    K-12 Senior High
                </div>
                <h3 class="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                    Discover Your Path
                </h3>
                <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                    Specialized tracks that align with your interests and prepare you for college and career success
                </p>
            </div>
            
            <!-- K-12 Programs Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
                {k12Programs.map((program, index) => (
                    <ProgramsVisualCard program={program} index={collegePrograms.length + index} />
                ))}
            </div>
        </div>
        
        
    </div>
</section>

<style>
    /* Enhanced animations */
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-10px) rotate(1deg); }
        66% { transform: translateY(5px) rotate(-1deg); }
    }
    
    @keyframes float-delayed {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(8px) rotate(-1deg); }
        66% { transform: translateY(-5px) rotate(1deg); }
    }
    
    @keyframes float-slow {
        0%, 100% { transform: translateY(0px) scale(1); }
        50% { transform: translateY(-15px) scale(1.05); }
    }
    
    .animate-float {
        animation: float 20s ease-in-out infinite;
    }
    
    .animate-float-delayed {
        animation: float-delayed 25s ease-in-out infinite;
        animation-delay: 2s;
    }
    
    .animate-float-slow {
        animation: float-slow 30s ease-in-out infinite;
        animation-delay: 5s;
    }
    
    /* Container improvements */
    .container {
        position: relative;
    }
    
    /* Section spacing improvements */
    section {
        scroll-margin-top: 6rem;
    }
    
    /* Grid enhancements */
    .grid {
        align-items: stretch;
    }
    
    /* Smooth scrolling */
    html {
        scroll-behavior: smooth;
    }
    
    /* Background gradient text */
    .bg-clip-text {
        -webkit-background-clip: text;
        background-clip: text;
    }
    
    /* Enhanced hover effects for buttons */
    button {
        will-change: transform;
    }
    
    /* Improved focus states */
    button:focus-visible {
        outline: 2px solid hsl(var(--primary));
        outline-offset: 2px;
    }
    
    /* Mobile optimizations */
    @media (max-width: 640px) {
        .container {
            padding-left: 1rem;
            padding-right: 1rem;
        }
        
        .grid {
            gap: 1rem;
        }
    }
    
    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .animate-float,
        .animate-float-delayed,
        .animate-float-slow {
            animation: none;
        }
        
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
    
    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .bg-gradient-to-br,
        .bg-gradient-to-r {
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }
    }
</style>
