---
// Define the props for this component
interface Specialization {
  name: string;
  icon: string;
}

interface Program {
  id: string;
  title: string;
  description: string;
  duration: string;
  format: string;
  degree: string;
  image: string;
  color: string;
  specializations: Specialization[];
  features: string[];
  learnMoreLink: string;
  category: string;
  isPopular?: boolean;
  isNew?: boolean;
}

interface Props {
  program: Program;
  index: number;
}

const { program, index } = Astro.props;

// Modern icon set with consistent styling
const getIconSvg = (iconName: string) => {
  const iconMap = {
    'code': `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg>`,
    'cloud': `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z"></path></svg>`,
    'shield': `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>`,
    'database': `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path><path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path></svg>`,
    'banknote': `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="12" x="2" y="6" rx="2"></rect><circle cx="12" cy="12" r="2"></circle><path d="M6 12h.01M18 12h.01"></path></svg>`,
    'presentation': `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 3h20"></path><path d="M21 3v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V3"></path><path d="m7 21 5-5 5 5"></path></svg>`,
    'users': `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>`,
    'lightbulb': `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5"></path><path d="M9 18h6"></path><path d="M10 22h4"></path></svg>`,
    'building': `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect><path d="M9 22v-4h6v4"></path><path d="M8 6h.01"></path><path d="M16 6h.01"></path><path d="M12 6h.01"></path><path d="M12 10h.01"></path><path d="M12 14h.01"></path><path d="M16 10h.01"></path><path d="M16 14h.01"></path><path d="M8 10h.01"></path><path d="M8 14h.01"></path></svg>`,
    'calendar': `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg>`,
    'map': `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="3 6 9 3 15 6 21 3 21 18 15 21 9 18 3 21"></polygon><line x1="9" x2="9" y1="3" y2="18"></line><line x1="15" x2="15" y1="6" y2="21"></line></svg>`,
    'utensils': `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path><path d="M7 2v20"></path><path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"></path></svg>`
  };
  
  return iconMap[iconName] || `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" x2="12" y1="8" y2="16"></line><line x1="8" x2="16" y1="12" y2="12"></line></svg>`;
};

// Get color classes based on program color
const getColorClasses = (color: string) => {
  const colorMap = {
    'blue': 'from-blue-500/20 to-blue-600/20 border-blue-200/50',
    'amber': 'from-amber-500/20 to-amber-600/20 border-amber-200/50',
    'green': 'from-green-500/20 to-green-600/20 border-green-200/50',
    'sky': 'from-sky-500/20 to-sky-600/20 border-sky-200/50',
    'orange': 'from-orange-500/20 to-orange-600/20 border-orange-200/50',
    'rose': 'from-rose-500/20 to-rose-600/20 border-rose-200/50',
    'lime': 'from-lime-500/20 to-lime-600/20 border-lime-200/50',
    'cyan': 'from-cyan-500/20 to-cyan-600/20 border-cyan-200/50'
  };
  return colorMap[color] || 'from-gray-500/20 to-gray-600/20 border-gray-200/50';
};
---

<div class="program-card-container" data-program-id={program.id} data-index={index}>
  <!-- Main Card -->
  <div class="program-card group cursor-pointer">
    <!-- Card Background with Dynamic Color -->
    <div class={`absolute inset-0 rounded-2xl bg-gradient-to-br ${getColorClasses(program.color)} opacity-0 group-hover:opacity-100 transition-all duration-500 ease-out`}></div>
    
    <!-- Card Content -->
    <div class="relative bg-card border border-border/40 rounded-2xl overflow-hidden group-hover:border-primary/30 group-hover:shadow-2xl group-hover:shadow-primary/10 transition-all duration-500 ease-out transform group-hover:-translate-y-2">
      
      <!-- Image Section with Overlay -->
      <div class="relative aspect-[4/3] overflow-hidden">
        <img 
          src={program.image} 
          alt={program.title}
          class="w-full h-full object-cover transition-transform duration-700 ease-out group-hover:scale-110"
        />
        
        <!-- Gradient Overlay -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-500"></div>
        
        <!-- Floating Badges -->
        <div class="absolute top-4 left-4 flex gap-2">
          {program.isPopular && (
            <span class="px-3 py-1 bg-primary text-primary-foreground text-xs font-semibold rounded-full shadow-lg backdrop-blur-sm animate-pulse">
              🔥 Popular
            </span>
          )}
          {program.isNew && (
            <span class="px-3 py-1 bg-emerald-500 text-white text-xs font-semibold rounded-full shadow-lg backdrop-blur-sm">
              ✨ New
            </span>
          )}
        </div>
        
        <!-- Category Badge -->
        <div class="absolute top-4 right-4">
          <span class="px-3 py-1 bg-white/20 text-white text-xs font-medium rounded-full backdrop-blur-md">
            {program.category}
          </span>
        </div>
        
        <!-- Program Title Overlay -->
        <div class="absolute bottom-4 left-4 right-4">
          <h3 class="text-white text-xl sm:text-2xl font-bold mb-2 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-500">
            {program.title}
          </h3>
          <div class="w-12 h-1 bg-primary rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 delay-200"></div>
        </div>
      </div>
      
      <!-- Content Section -->
      <div class="p-6">
        <!-- Description -->
        <p class="text-muted-foreground mb-4 line-clamp-2 group-hover:line-clamp-none transition-all duration-300">
          {program.description}
        </p>
        
        <!-- Quick Info Pills -->
        <div class="flex flex-wrap gap-2 mb-4">
          <div class="flex items-center gap-1 px-3 py-1 bg-secondary/60 rounded-full text-xs">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
            <span>{program.duration}</span>
          </div>
          <div class="flex items-center gap-1 px-3 py-1 bg-secondary/60 rounded-full text-xs">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
              <polyline points="9 22 9 12 15 12 15 22"></polyline>
            </svg>
            <span>{program.format}</span>
          </div>
        </div>
        
        <!-- Action Button -->
        <button class="w-full py-3 bg-primary/10 hover:bg-primary hover:text-primary-foreground text-primary rounded-lg font-medium transition-all duration-300 transform hover:scale-105 active:scale-95">
          Explore Program
        </button>
      </div>
    </div>
    
    <!-- Hover Overlay for Desktop -->
    <div class="program-details-overlay absolute inset-0 bg-card/95 backdrop-blur-sm rounded-2xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-500 ease-out transform scale-95 group-hover:scale-100 hidden lg:block">
      <div class="p-6 h-full flex flex-col">
        <!-- Header -->
        <div class="mb-6">
          <h3 class="text-2xl font-bold mb-2">{program.title}</h3>
          <p class="text-muted-foreground">{program.description}</p>
        </div>
        
        <!-- Specializations -->
        <div class="mb-6 flex-1">
          <h4 class="text-sm font-semibold text-primary mb-3">Specializations</h4>
          <div class="grid grid-cols-2 gap-2">
            {program.specializations.map((spec, i) => (
              <div class="flex items-center gap-2 p-2 rounded-lg bg-secondary/30 transform translate-x-4 opacity-0 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-500" style={`animation-delay: ${i * 100}ms`}>
                <span class="text-primary" set:html={getIconSvg(spec.icon)} />
                <span class="text-sm">{spec.name}</span>
              </div>
            ))}
          </div>
        </div>
        
        <!-- Features -->
        <div class="mb-6">
          <h4 class="text-sm font-semibold text-primary mb-3">Key Features</h4>
          <ul class="space-y-2">
            {program.features.map((feature, i) => (
              <li class="flex items-center gap-2 text-sm transform translate-x-4 opacity-0 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-500" style={`animation-delay: ${(i + program.specializations.length) * 100}ms`}>
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-emerald-500 flex-shrink-0">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
                <span>{feature}</span>
              </li>
            ))}
          </ul>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex gap-3">
          <button class="flex-1 py-2 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors">
            Learn More
          </button>
          <button class="flex-1 py-2 border border-border text-foreground rounded-lg font-medium hover:bg-secondary/50 transition-colors">
            Apply Now
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Mobile Modal -->
<div class="mobile-modal-overlay fixed inset-0 bg-black/50 backdrop-blur-sm z-50 opacity-0 invisible transition-all duration-300" data-program-id={program.id}>
  <div class="mobile-modal absolute bottom-0 left-0 right-0 bg-card rounded-t-3xl transform translate-y-full transition-transform duration-500 ease-out max-h-[80vh] overflow-y-auto">
    <div class="p-6">
      <!-- Modal Header -->
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-xl font-bold">{program.title}</h3>
        <button class="modal-close p-2 rounded-full hover:bg-secondary/50 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="m18 6-12 12"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      </div>
      
      <!-- Modal Content -->
      <div class="space-y-6">
        <p class="text-muted-foreground">{program.description}</p>
        
        <!-- Quick Info -->
        <div class="grid grid-cols-3 gap-4 p-4 bg-secondary/30 rounded-xl">
          <div class="text-center">
            <div class="text-sm text-muted-foreground">Duration</div>
            <div class="font-medium">{program.duration}</div>
          </div>
          <div class="text-center">
            <div class="text-sm text-muted-foreground">Format</div>
            <div class="font-medium">{program.format}</div>
          </div>
          <div class="text-center">
            <div class="text-sm text-muted-foreground">Degree</div>
            <div class="font-medium text-sm">{program.degree}</div>
          </div>
        </div>
        
        <!-- Specializations -->
        <div>
          <h4 class="font-semibold mb-3 text-primary">Specializations</h4>
          <div class="grid grid-cols-1 gap-2">
            {program.specializations.map((spec) => (
              <div class="flex items-center gap-3 p-3 bg-secondary/30 rounded-lg">
                <span class="text-primary" set:html={getIconSvg(spec.icon)} />
                <span>{spec.name}</span>
              </div>
            ))}
          </div>
        </div>
        
        <!-- Features -->
        <div>
          <h4 class="font-semibold mb-3 text-primary">Program Features</h4>
          <ul class="space-y-2">
            {program.features.map((feature) => (
              <li class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-emerald-500 flex-shrink-0">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
                <span>{feature}</span>
              </li>
            ))}
          </ul>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex gap-3 pt-4">
          <button class="flex-1 py-3 bg-primary text-primary-foreground rounded-lg font-medium">
            Learn More
          </button>
          <button class="flex-1 py-3 border border-border text-foreground rounded-lg font-medium">
            Apply Now
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script define:vars={{ programId: program.id }}>
  document.addEventListener('DOMContentLoaded', () => {
    const card = document.querySelector(`[data-program-id="${programId}"] .program-card`);
    const mobileModal = document.querySelector(`.mobile-modal-overlay[data-program-id="${programId}"]`);
    const modalClose = mobileModal?.querySelector('.modal-close');
    const modal = mobileModal?.querySelector('.mobile-modal');
    
    // Mobile tap interaction
    if (window.innerWidth < 1024) {
      card?.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        
        // Show modal
        mobileModal?.classList.remove('opacity-0', 'invisible');
        setTimeout(() => {
          modal?.classList.remove('translate-y-full');
        }, 50);
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
      });
    }
    
    // Close modal
    const closeModal = () => {
      modal?.classList.add('translate-y-full');
      setTimeout(() => {
        mobileModal?.classList.add('opacity-0', 'invisible');
        document.body.style.overflow = '';
      }, 300);
    };
    
    modalClose?.addEventListener('click', closeModal);
    mobileModal?.addEventListener('click', (e) => {
      if (e.target === mobileModal) closeModal();
    });
    
    // Escape key to close modal
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !mobileModal?.classList.contains('invisible')) {
        closeModal();
      }
    });
    
    // Handle window resize
    window.addEventListener('resize', () => {
      if (window.innerWidth >= 1024 && !mobileModal?.classList.contains('invisible')) {
        closeModal();
      }
    });
  });
</script>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-none {
    display: block;
    -webkit-line-clamp: unset;
    -webkit-box-orient: unset;
    overflow: visible;
  }
  
  .program-card-container {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease forwards;
  }
  
  .program-card-container:nth-child(1) { animation-delay: 0.1s; }
  .program-card-container:nth-child(2) { animation-delay: 0.2s; }
  .program-card-container:nth-child(3) { animation-delay: 0.3s; }
  .program-card-container:nth-child(4) { animation-delay: 0.4s; }
  .program-card-container:nth-child(5) { animation-delay: 0.5s; }
  .program-card-container:nth-child(6) { animation-delay: 0.6s; }
  .program-card-container:nth-child(7) { animation-delay: 0.7s; }
  .program-card-container:nth-child(8) { animation-delay: 0.8s; }
  
  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Smooth hover transitions */
  .program-card {
    position: relative;
    will-change: transform;
  }
  
  .program-details-overlay {
    will-change: opacity, transform;
  }
  
  /* Mobile modal animations */
  .mobile-modal-overlay {
    will-change: opacity;
  }
  
  .mobile-modal {
    will-change: transform;
  }
  
  /* Prevent scroll on body when modal is open */
  body.modal-open {
    overflow: hidden;
  }
  
  /* Improved focus states for accessibility */
  .program-card:focus-within {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }
  
  /* Ensure consistent spacing */
  .program-card-container {
    height: 100%;
  }
  
  .program-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .program-card-container,
    .program-card,
    .program-details-overlay,
    .mobile-modal,
    .mobile-modal-overlay {
      animation: none !important;
      transition: none !important;
    }
    
    .program-card img {
      transform: none !important;
    }
  }
</style>
