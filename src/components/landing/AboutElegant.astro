---
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
---

<section id="about" class="relative overflow-hidden py-14 sm:py-20 md:py-32">
  <!-- Elegant background elements -->
  <div class="absolute inset-0 -z-10">
    <!-- Base background -->
    <div class="absolute inset-0 bg-background"></div>
    
    <!-- Subtle gradient overlay -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-background"></div>
    
    <!-- Elegant decorative elements -->
    <div class="absolute right-0 top-0 h-[20rem] w-[20rem] sm:h-[25rem] sm:w-[25rem] md:h-[30rem] md:w-[30rem] translate-x-1/3 -translate-y-1/3 rounded-full border border-primary/5 opacity-70"></div>
    <div class="absolute bottom-0 left-0 h-[15rem] w-[15rem] sm:h-[20rem] sm:w-[20rem] md:h-[25rem] md:w-[25rem] -translate-x-1/3 translate-y-1/3 rounded-full border border-primary/5 opacity-70"></div>
  </div>

  <div class="container relative z-10 px-3 sm:px-6 lg:px-8">
    <!-- Elegant section header with smooth reveal -->
    <div class="smooth-reveal mb-12 sm:mb-16 md:mb-20">
      <div class="mx-auto max-w-md px-2 sm:px-4 text-center">
        <div class="inline-flex items-center justify-center">
          <Separator className="w-12 bg-primary/30" />
          <span class="mx-4 text-sm uppercase tracking-widest text-primary">About Us</span>
          <Separator className="w-12 bg-primary/30" />
        </div>
        
        <h2 class="mt-4 text-2xl font-light tracking-tight sm:text-4xl md:text-5xl lg:text-6xl">
          Our <span class="gradient-text font-medium">Story</span>
        </h2>
        
        <p class="mt-6 text-balance text-muted-foreground">
          A journey of excellence in education, innovation, and transformation
        </p>
      </div>
    </div>

    <!-- Elegant two-column layout -->
    <div class="mb-16 sm:mb-20 md:mb-24 grid gap-10 sm:gap-12 md:gap-16 md:grid-cols-2">
      <!-- Left column: Image with elegant border -->
      <div class="smooth-reveal-left relative order-2 md:order-1">
        <div class="elegant-frame relative aspect-[4/5] overflow-hidden rounded-xl border border-primary/20 shadow-lg shadow-primary/5">
          <div class="absolute inset-0 flex items-center justify-center overflow-hidden">
            <!-- Founder image with gradient overlay -->
            <img 
              src="/founder.png" 
              alt="Founder of Data Center College of The Philippines" 
              class="h-full w-full object-cover transition-transform duration-1000 hover:scale-105"
            />
            <div class="absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-transparent"></div>
          </div>
          
          <!-- Caption for the founder -->
          <div class="absolute bottom-0 left-0 right-0 bg-background/80 p-4 backdrop-blur-md">
            <div class="group relative">
              <Badge variant="outline" className="mb-2 border-primary/30 bg-primary/5">Founder</Badge>
              <h3 class="text-xl font-medium">Engr. Wilfredo M. Bactad</h3>
              <p class="text-sm text-muted-foreground">Founder since 1974</p>
              <div class="mt-2 h-px w-12 bg-primary transition-all duration-500 ease-out group-hover:w-full"></div>
            </div>
          </div>
          
          <!-- Elegant corner accents -->
          <div class="absolute left-0 top-0 h-12 w-12 border-l-2 border-t-2 border-primary/30"></div>
          <div class="absolute right-0 top-0 h-12 w-12 border-r-2 border-t-2 border-primary/30"></div>
          <div class="absolute bottom-0 left-0 h-12 w-12 border-b-2 border-l-2 border-primary/30"></div>
          <div class="absolute bottom-0 right-0 h-12 w-12 border-b-2 border-r-2 border-primary/30"></div>
        </div>
        
        <!-- Elegant floating accent -->
        <div class="absolute -bottom-6 -right-6 h-24 w-24 sm:h-28 sm:w-28 md:h-32 md:w-32 sm:-bottom-8 sm:-right-8 rounded-full border border-primary/10 bg-primary/5 animate-float-delayed"></div>
        <div class="absolute -top-6 -left-6 h-16 w-16 sm:h-20 sm:w-20 md:h-24 md:w-24 sm:-top-8 sm:-left-8 rounded-full border border-primary/10 bg-primary/5 animate-float"></div>
      </div>
      
      <!-- Right column: Content with elegant typography -->
      <div class="smooth-reveal-right order-1 flex flex-col justify-center px-2 sm:px-0 md:order-2">
        <div class="group relative mb-4 sm:mb-6 inline-flex items-center rounded-full border border-primary/30 bg-background/80 px-3 sm:px-4 py-1.5 text-xs sm:text-sm font-medium text-primary backdrop-blur-md transition-all duration-300 hover:border-primary/60">
          <div class="absolute -inset-[1px] -z-10 rounded-full bg-gradient-to-r from-primary/20 via-primary/0 to-primary/20 opacity-0 blur-md transition-all duration-500 group-hover:opacity-100"></div>
          <span class="mr-2 inline-block h-2 w-2 animate-pulse rounded-full bg-primary"></span>
          Excellence Since 1974
        </div>
        
        <p class="elegant-dropcap text-sm sm:text-base md:text-lg leading-relaxed text-foreground">
          Founded in 1974, Data Center College of The Philippines was established by Engr. Wilfredo M. Bactad with a vision to bridge the gap between academic learning and industry requirements. Our institution has evolved into a premier center for technology and business education in Baguio City.
        </p>
        
        <p class="mt-6 text-muted-foreground">
          Our college has grown from a small institution to a respected name in education, particularly in the fields of information technology, business administration, and hospitality management. We continue to evolve our programs to meet the changing demands of the global job market for nearly five decades.
        </p>
        
        <div class="mt-6 sm:mt-8 flex items-center space-x-4">
          <div class="h-px flex-1 bg-gradient-to-r from-primary/5 via-primary/30 to-primary/5"></div>
          <span class="font-medium text-primary">Est. 1974</span>
          <div class="h-px flex-1 bg-gradient-to-r from-primary/5 via-primary/30 to-primary/5"></div>
        </div>
        
        <div class="mt-6 sm:mt-8">
          <Button size="lg" className="group relative overflow-hidden bg-primary px-6 sm:px-8 py-5 sm:py-6 text-base sm:text-lg">
            <span class="relative z-10 flex items-center font-medium">
              Learn more about our history
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1">
                <path d="M5 12h14"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
            </span>
            <span class="absolute inset-0 -z-10 bg-gradient-to-r from-primary to-primary/80 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></span>
          </Button>
        </div>
      </div>
    </div>

    <!-- Elegant values section with smooth hover effects -->
    <div class="mb-16 sm:mb-20 md:mb-24">
      <div class="smooth-reveal mb-16 text-center">
        <h3 class="text-2xl sm:text-3xl font-light">Our Core <span class="gradient-text font-medium">Values</span></h3>
      </div>
      
      <div class="grid gap-4 sm:gap-6 md:gap-8 px-2 sm:px-0 sm:grid-cols-2 lg:grid-cols-3">
        <!-- Value 1 -->
        <div class="smooth-reveal-staggered group" data-stagger="0" style="--stagger: 0">
          <div class="relative overflow-hidden rounded-xl border border-border/40 bg-background/60 p-6 backdrop-blur-md transition-all duration-300 hover:-translate-y-1 hover:border-primary/30 hover:shadow-lg hover:shadow-primary/5">
            <div class="absolute -right-6 -top-6 h-12 w-12 rounded-full bg-primary/10"></div>
            <div class="relative">
              <div class="mb-4 sm:mb-6 inline-flex h-12 w-12 sm:h-16 sm:w-16 items-center justify-center rounded-full bg-primary/5 transition-colors duration-500 ease-out group-hover:bg-primary/10">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-8 w-8 text-primary"><path d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c3 3 9 3 12 0v-5"/></svg>
              </div>
              
              <h4 class="text-lg sm:text-xl font-medium">Excellence in Education</h4>
              <div class="mt-2 h-px w-12 bg-primary transition-all duration-500 ease-out group-hover:w-24"></div>
              <p class="mt-4 text-muted-foreground">
                Committed to providing high-quality education with modern teaching methodologies and industry-relevant curriculum.
              </p>
            </div>
            <div class="absolute -inset-[1px] -z-10 rounded-xl bg-gradient-to-r from-primary/20 via-primary/0 to-primary/20 opacity-0 blur-md transition-all duration-500 group-hover:opacity-100"></div>
          </div>
        </div>
        
        <!-- Value 2 -->
        <div class="smooth-reveal-staggered group" data-stagger="1" style="--stagger: 1">
          <div class="relative overflow-hidden rounded-xl border border-border/40 bg-background/60 p-6 backdrop-blur-md transition-all duration-300 hover:-translate-y-1 hover:border-primary/30 hover:shadow-lg hover:shadow-primary/5">
            <div class="absolute -right-6 -top-6 h-12 w-12 rounded-full bg-primary/10"></div>
            <div class="relative">
              <div class="mb-4 sm:mb-6 inline-flex h-12 w-12 sm:h-16 sm:w-16 items-center justify-center rounded-full bg-primary/5 transition-colors duration-500 ease-out group-hover:bg-primary/10">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 sm:h-8 sm:w-8 text-primary"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>
              </div>
              
              <h4 class="text-lg sm:text-xl font-medium">Student-Centered Approach</h4>
              <div class="mt-2 h-px w-12 bg-primary transition-all duration-500 ease-out group-hover:w-24"></div>
              <p class="mt-4 text-muted-foreground">
                Focusing on individual student needs with personalized attention and mentorship to ensure academic success.
              </p>
            </div>
            <div class="absolute -inset-[1px] -z-10 rounded-xl bg-gradient-to-r from-primary/20 via-primary/0 to-primary/20 opacity-0 blur-md transition-all duration-500 group-hover:opacity-100"></div>
          </div>
        </div>
        
        <!-- Value 3 -->
        <div class="smooth-reveal-staggered group" data-stagger="2" style="--stagger: 2">
          <div class="relative overflow-hidden rounded-xl border border-border/40 bg-background/60 p-6 backdrop-blur-md transition-all duration-300 hover:-translate-y-1 hover:border-primary/30 hover:shadow-lg hover:shadow-primary/5">
            <div class="absolute -right-6 -top-6 h-12 w-12 rounded-full bg-primary/10"></div>
            <div class="relative">
              <div class="mb-4 sm:mb-6 inline-flex h-12 w-12 sm:h-16 sm:w-16 items-center justify-center rounded-full bg-primary/5 transition-colors duration-500 ease-out group-hover:bg-primary/10">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 sm:h-8 sm:w-8 text-primary"><path d="M12 20h9"/><path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z"/></svg>
              </div>
              
              <h4 class="text-lg sm:text-xl font-medium">Industry Connections</h4>
              <div class="mt-2 h-px w-12 bg-primary transition-all duration-500 ease-out group-hover:w-24"></div>
              <p class="mt-4 text-muted-foreground">
                Strong partnerships with leading companies providing internships, job placements, and industry exposure.
              </p>
            </div>
            <div class="absolute -inset-[1px] -z-10 rounded-xl bg-gradient-to-r from-primary/20 via-primary/0 to-primary/20 opacity-0 blur-md transition-all duration-500 group-hover:opacity-100"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Elegant timeline with smooth scroll animations -->
    <div class="mb-16 sm:mb-20 md:mb-24">
      <div class="smooth-reveal mb-10 sm:mb-16 text-center">
        <h3 class="text-2xl sm:text-3xl font-light">Our <span class="gradient-text font-medium">Journey</span></h3>
      </div>
      
      <div class="relative">
        <!-- Elegant timeline line -->
        <div class="absolute left-8 sm:left-0 top-0 bottom-0 h-full w-px bg-gradient-to-b from-primary/0 via-primary/30 to-primary/0 md:left-1/2"></div>
        
        <!-- Timeline items -->
        <div class="space-y-16 sm:space-y-20 md:space-y-24">
          <!-- Timeline item 1 -->
          <div class="relative">
            <div class="absolute left-8 sm:left-0 top-0 flex h-6 w-6 -translate-x-1/2 items-center justify-center rounded-full border border-primary/30 bg-background md:left-1/2">
              <div class="h-2 w-2 rounded-full bg-primary"></div>
            </div>
            
            <div class="ml-16 sm:ml-8 md:ml-0 md:grid md:grid-cols-2 md:gap-8">
              <div class="smooth-reveal-right md:text-right">
                <div class="inline-block rounded-full border border-primary/20 bg-primary/5 px-3 py-1 text-xs sm:text-sm font-light text-primary">1974</div>
                <h4 class="mt-2 text-lg sm:text-xl font-medium">Foundation</h4>
              </div>
              
              <div class="smooth-reveal-left mt-2 md:mt-0">
                <p class="text-muted-foreground">
                  Established with a mission to provide cutting-edge technology education in the heart of Baguio City, the Summer Capital.
                </p>
              </div>
            </div>
          </div>
          
          <!-- Timeline item 2 -->
          <div class="relative">
            <div class="absolute left-8 sm:left-0 top-0 flex h-6 w-6 -translate-x-1/2 items-center justify-center rounded-full border border-primary/30 bg-background md:left-1/2">
              <div class="h-2 w-2 rounded-full bg-primary"></div>
            </div>
            
            <div class="ml-16 sm:ml-8 md:ml-0 md:grid md:grid-cols-2 md:gap-8">
              <div class="smooth-reveal-right md:text-right">
                <div class="inline-block rounded-full border border-primary/20 bg-primary/5 px-3 py-1 text-xs sm:text-sm font-light text-primary">2005</div>
                <h4 class="mt-2 text-lg sm:text-xl font-medium">Expansion</h4>
              </div>
              
              <div class="smooth-reveal-left mt-2 md:mt-0">
                <p class="text-muted-foreground">
                  Expanded programs and facilities to accommodate growing student population and new course offerings.
                </p>
              </div>
            </div>
          </div>
          
          <!-- Timeline item 3 -->
          <div class="relative">
            <div class="absolute left-8 sm:left-0 top-0 flex h-6 w-6 -translate-x-1/2 items-center justify-center rounded-full border border-primary/30 bg-background md:left-1/2">
              <div class="h-2 w-2 rounded-full bg-primary"></div>
            </div>
            
            <div class="ml-16 sm:ml-8 md:ml-0 md:grid md:grid-cols-2 md:gap-8">
              <div class="smooth-reveal-right md:text-right">
                <div class="inline-block rounded-full border border-primary/20 bg-primary/5 px-3 py-1 text-xs sm:text-sm font-light text-primary">2015</div>
                <h4 class="mt-2 text-lg sm:text-xl font-medium">Innovation Hub</h4>
              </div>
              
              <div class="smooth-reveal-left mt-2 md:mt-0">
                <p class="text-muted-foreground">
                  Launched technology innovation center for advanced research and development, strengthening industry partnerships.
                </p>
              </div>
            </div>
          </div>
          
          <!-- Timeline item 4 -->
          <div class="relative">
            <div class="absolute left-8 sm:left-0 top-0 flex h-6 w-6 -translate-x-1/2 items-center justify-center rounded-full border border-primary/30 bg-background md:left-1/2">
              <div class="h-2 w-2 rounded-full bg-primary"></div>
            </div>
            
            <div class="ml-16 sm:ml-8 md:ml-0 md:grid md:grid-cols-2 md:gap-8">
              <div class="smooth-reveal-right md:text-right">
                <div class="inline-block rounded-full border border-primary/20 bg-primary/5 px-3 py-1 text-xs sm:text-sm font-light text-primary">Present</div>
                <h4 class="mt-2 text-lg sm:text-xl font-medium">Digital Transformation</h4>
              </div>
              
              <div class="smooth-reveal-left mt-2 md:mt-0">
                <p class="text-muted-foreground">
                  Leading the way in digital education and preparing students for Industry 4.0 with cutting-edge programs.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Elegant stats with smooth counter animations -->
      <div class="smooth-reveal px-2 sm:px-0">
        <div class="overflow-hidden rounded-xl border border-primary/10 bg-card shadow-md">
        <div class="grid divide-y sm:grid-cols-2 lg:grid-cols-4 sm:divide-x sm:divide-y-0">
          <!-- Stat 1 -->
          <div class="elegant-stat group p-6 sm:p-8 text-center transition-colors duration-500 ease-out hover:bg-primary/5">
            <div class="text-3xl sm:text-4xl font-light text-primary">
              <span class="counter inline-block" data-target="50">0</span>+
            </div>
            <p class="mt-2 text-sm text-muted-foreground">Years of Excellence</p>
            <div class="mx-auto mt-4 h-px w-0 bg-primary transition-all duration-500 ease-out group-hover:w-12"></div>
          </div>
          
          <!-- Stat 2 -->
          <div class="elegant-stat group p-6 sm:p-8 text-center transition-colors duration-500 ease-out hover:bg-primary/5">
            <div class="text-3xl sm:text-4xl font-light text-primary">
              <span class="counter inline-block" data-target="5000">0</span>+
            </div>
            <p class="mt-2 text-sm text-muted-foreground">Graduates</p>
            <div class="mx-auto mt-4 h-px w-0 bg-primary transition-all duration-500 ease-out group-hover:w-12"></div>
          </div>
          
          <!-- Stat 3 -->
          <div class="elegant-stat group p-6 sm:p-8 text-center transition-colors duration-500 ease-out hover:bg-primary/5">
            <div class="text-3xl sm:text-4xl font-light text-primary">
              <span class="counter inline-block" data-target="95">0</span>%
            </div>
            <p class="mt-2 text-sm text-muted-foreground">Employment Rate</p>
            <div class="mx-auto mt-4 h-px w-0 bg-primary transition-all duration-500 ease-out group-hover:w-12"></div>
          </div>
          
          <!-- Stat 4 -->
          <div class="elegant-stat group p-6 sm:p-8 text-center transition-colors duration-500 ease-out hover:bg-primary/5">
            <div class="text-3xl sm:text-4xl font-light text-primary">
              <span class="counter inline-block" data-target="50">0</span>+
            </div>
            <p class="mt-2 text-sm text-muted-foreground">Industry Partners</p>
            <div class="mx-auto mt-4 h-px w-0 bg-primary transition-all duration-500 ease-out group-hover:w-12"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  /* Mobile-first responsive styling */
  /* Gradient text style from HeroAvantGardeNew */
  .gradient-text {
    background: linear-gradient(to right, hsl(var(--primary)), hsl(var(--primary) / 0.8));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
    position: relative;
  }
  
  /* Elegant drop cap */
  .elegant-dropcap::first-letter {
    float: left;
    font-size: 3em;
    line-height: 0.8;
    margin-right: 0.1em;
    font-weight: 300;
    color: hsl(var(--primary));
  }
  
  @media (min-width: 640px) {
    .elegant-dropcap::first-letter {
      font-size: 3.5em;
    }
  }
  
  /* Elegant frame */
  .elegant-frame {
    position: relative;
    transition: all 0.5s ease-out;
  }
  
  .elegant-frame:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px -10px hsl(var(--primary) / 0.2);
  }
  
  /* Animation keyframes from HeroAvantGardeNew */
  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-6px);
    }
  }
  
  @keyframes float-delayed {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-6px);
    }
  }
  
  @media (min-width: 640px) {
    @keyframes float {
      0%, 100% {
        transform: translateY(0);
      }
      50% {
        transform: translateY(-10px);
      }
    }
    
    @keyframes float-delayed {
      0%, 100% {
        transform: translateY(0);
      }
      50% {
        transform: translateY(-10px);
      }
    }
  }
  
  /* Apply animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
    /* Improve performance on mobile */
    will-change: transform;
  }
  
  .animate-float-delayed {
    animation: float-delayed 7s ease-in-out 1s infinite;
    /* Improve performance on mobile */
    will-change: transform;
  }
  
  /* Reduce animation on devices that prefer reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .animate-float, .animate-float-delayed {
      animation: none;
    }
  }
  
  /* Smooth reveal animations */
  .smooth-reveal, .smooth-reveal-left, .smooth-reveal-right, .smooth-reveal-staggered {
    opacity: 0;
    transition: all 1s cubic-bezier(0.22, 1, 0.36, 1);
    /* Ensure smoother animation performance */
    will-change: opacity, transform;
    backface-visibility: hidden;
  }
  
  .smooth-reveal {
    transform: translateY(20px);
  }
  
  .smooth-reveal-left {
    transform: translateX(-20px);
  }
  
  .smooth-reveal-right {
    transform: translateX(20px);
  }
  
  .smooth-reveal-staggered {
    transform: translateY(20px);
    transition-delay: calc(var(--stagger) * 0.1s + 0.1s);
  }
  
  .reveal-active {
    opacity: 1;
    transform: translate(0);
  }
  
  /* Dark mode compatibility */
  :global(.dark) .elegant-dropcap::first-letter {
    color: hsl(var(--primary) / 0.9);
  }
  
  :global(.dark) .from-background\/80 {
    --tw-gradient-from: hsl(var(--background) / 0.8);
  }
  
  :global(.dark) .bg-background\/60 {
    background-color: hsl(var(--background) / 0.6);
  }
  
  :global(.dark) .bg-background\/80 {
    background-color: hsl(var(--background) / 0.8);
  }
  
  /* Mobile-specific adjustments */
  @media (max-width: 640px) {
    .container {
      max-width: 100%;
    }
  
    .elegant-stat {
      padding-top: 1rem;
      padding-bottom: 1rem;
    }
  
    /* Adjust spacing for mobile */
    .space-y-16 > * + * {
      margin-top: 3rem;
    }
    
    .space-y-24 > * + * {
      margin-top: 4rem; /* Reduce from 6rem (24 * 0.25rem) */
    }
  
    /* Make the timeline more compact */
    .timeline-item {
      margin-bottom: 3rem;
    }
  
    /* Make images display better on small screens */
    img {
      max-width: 100%;
      height: auto;
    }
    
    /* Optimize button size on small screens */
    .button {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
    }
  }
  
    /* Extra small screen support */
    @media (max-width: 479px) {
      .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
      }
    
      .elegant-frame {
        margin-left: 0.5rem;
        margin-right: 0.5rem;
      }
    
      h3 {
        font-size: 1.25rem;
      }
      
      h4 {
        font-size: 1rem;
      }
    
      .counter {
        font-size: 1.5rem;
      }
    
      .elegant-dropcap::first-letter {
        font-size: 2.5em;
      }
      
      .smooth-reveal, .smooth-reveal-left, .smooth-reveal-right {
        transition-duration: 0.6s;
      }
    }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    // Smooth reveal animations with adjusted options for mobile
    const isMobile = window.innerWidth < 768;
    const observerOptions = {
      threshold: isMobile ? 0.05 : 0.1,
      rootMargin: isMobile ? '0px 0px -50px 0px' : '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // If user prefers reduced motion, add class immediately without delay
          if (prefersReducedMotion) {
            entry.target.classList.add('reveal-active');
          } else {
            // Add small timeout for better performance on mobile
            setTimeout(() => {
              entry.target.classList.add('reveal-active');
            }, isMobile ? 50 : 0);
          }
        }
      });
    }, observerOptions);
    
    // Observe all elements with reveal animations
    document.querySelectorAll('.smooth-reveal, .smooth-reveal-left, .smooth-reveal-right, .smooth-reveal-staggered').forEach(el => {
      observer.observe(el);
    });
    
    // Counter animation with smooth easing
    const counters = document.querySelectorAll('.counter');
    
    const counterObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const counter = entry.target as HTMLElement;
          const targetAttr = counter.getAttribute('data-target');
          const target = targetAttr ? parseInt(targetAttr, 10) : 0;
          
          // Smooth counter animation with easing
          const duration = 2000; // ms
          const frameDuration = 1000/60; // 60fps
          const totalFrames = Math.round(duration / frameDuration);
          
          let frame = 0;
          const easeOutQuad = (t: number): number => t * (2 - t);
          
          const animate = () => {
            frame++;
            const progress = easeOutQuad(frame / totalFrames);
            const currentCount = Math.round(target * progress);
            
            const currentText = currentCount.toString();
            if (counter.textContent !== currentText) {
              counter.textContent = currentText;
            }
            
            if (frame < totalFrames) {
              requestAnimationFrame(animate);
            } else {
              counter.textContent = target.toString();
            }
          };
          
          animate();
        }
      });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => {
      counterObserver.observe(counter);
    });
    
    // Handle resize events with debounce for better performance
    let resizeTimer;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(() => {
        const newIsMobile = window.innerWidth < 768;
        if (newIsMobile !== isMobile) {
          // Only reload if mobile state actually changed
          observer.disconnect();
          counterObserver.disconnect();
          
          // Re-observe elements after a small delay
          setTimeout(() => {
            document.querySelectorAll('.smooth-reveal, .smooth-reveal-left, .smooth-reveal-right, .smooth-reveal-staggered').forEach(el => {
              observer.observe(el);
            });
            
            counters.forEach(counter => {
              counterObserver.observe(counter);
            });
          }, 100);
        }
      }, 250);
    });
  });
</script>
