---
// Define the props for this component
interface Facility {
  id: string;
  title: string;
  description: string;
  icon: string;
  image: string;
  color: string;
  features: string[];
  highlight?: boolean;
}

interface Props {
  facility: Facility;
  index: number;
}

const { facility, index } = Astro.props;

// Function to get icon SVG based on name
const getIconSvg = (iconName: string) => {
  switch(iconName) {
    case 'computer':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="14" x="2" y="3" rx="2"/><line x1="8" x2="16" y1="21" y2="21"/><line x1="12" x2="12" y1="17" y2="21"/></svg>`;
    case 'book':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/></svg>`;
    case 'utensils':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"/><path d="M7 2v20"/><path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"/></svg>`;
    case 'users':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>`;
    case 'presentation':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="3" rx="2"/><path d="M7 7v10"/><path d="M11 7v10"/><path d="m15 7 2 10"/></svg>`;
    case 'activity':
      return `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z"/><path d="m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65"/><path d="m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65"/></svg>`;
    default:
      return `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle></svg>`;
  }
};
---

<div class="facility-card group relative animate-slide-up" data-index={index} style={`--slide-delay: ${index * 100}ms;`}>
  <div class={`relative overflow-hidden rounded-xl border ${facility.highlight ? 'border-primary shadow-primary/20' : 'border-border/40'} bg-background/60 backdrop-blur-md transition-all duration-300 hover:-translate-y-1 hover:shadow-xl hover:shadow-primary/10 flex flex-col h-full`}>
    <div class="relative aspect-[16/9] w-full overflow-hidden">
      <img src={facility.image} alt={facility.title} class="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105" />
      <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/10"></div>
      {facility.highlight && (
        <div class="absolute top-3 right-3 px-3 py-1 bg-primary text-primary-foreground text-xs font-semibold rounded-full z-10 card-badge">
          FEATURED
        </div>
      )}
    </div>
    
    <div class="p-6 flex flex-col flex-grow">
      <div class="flex items-start gap-4 mb-4">
        <div class="mt-1 flex-shrink-0 rounded-lg bg-primary/10 p-2.5 text-primary card-icon">
          <Fragment set:html={getIconSvg(facility.icon)} />
        </div>
        <div>
          <h3 class="text-xl font-semibold mb-1 card-title text-foreground group-hover:text-primary transition-colors">
            {facility.title}
          </h3>
          <p class="text-sm text-muted-foreground card-description line-clamp-3">
            {facility.description}
          </p>
        </div>
      </div>
      
      <div class="space-y-2 mb-6 card-features flex-grow">
        {facility.features.slice(0, 3).map((feature, i) => ( // Show max 3 features for brevity
          <div class="flex items-center gap-2 text-sm text-muted-foreground feature-item" style={`--delay: ${i * 100}ms`}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-primary/70 flex-shrink-0">
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
            <span>{feature}</span>
          </div>
        ))}
      </div>
      
      <div class="mt-auto card-button">
        <a href={`#facility-${facility.id}`} class="inline-flex items-center text-sm font-medium text-primary hover:text-primary/80 transition-colors group/link">
          <span class="relative">
            Learn More
            <span class="absolute -bottom-0.5 left-0 w-full h-px bg-current transform origin-left scale-x-0 group-hover/link:scale-x-100 transition-transform duration-300"></span>
          </span>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1.5 transform translate-x-0 group-hover/link:translate-x-0.5 transition-transform duration-300">
            <path d="M5 12h14"></path>
            <path d="m12 5 7 7-7 7"></path>
          </svg>
        </a>
      </div>
    </div>
    
    <!-- Subtle decorative element similar to HeroAvantGardeNew cards -->
    <div class="absolute -right-8 -top-8 h-20 w-20 rounded-full bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
  </div>
</div>

<script define:vars={{ index }}>
  // Using CSS for initial reveal via animate-slide-up with --slide-delay
  // The more complex JS intersection observer for internal elements can be simplified or removed if CSS animations suffice.
  // For this redesign, we are relying on the new `animate-slide-up` class and CSS transitions for hover states.
  // If more granular, scroll-dependent animations are still desired for internal parts, the JS below can be adapted.

  // Simplified JS for potential future use or if CSS animations are not enough.
  /*
  document.addEventListener('DOMContentLoaded', () => {
    const facilityCard = document.querySelector(`.facility-card[data-index="${index}"]`);
    
    if (facilityCard) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            // Add class to trigger CSS defined animations for internal elements
            facilityCard.classList.add('internal-elements-visible');
            observer.unobserve(entry.target);
          }
        });
      }, { threshold: 0.1 });
      
      observer.observe(facilityCard);
    }
  });
  */
</script>

<style>
  /* Basic slide-up animation (can be global) */
  @keyframes slide-up {
    0% {
      transform: translateY(40px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
  .animate-slide-up {
    animation: slide-up 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    animation-delay: var(--slide-delay, 0s);
    opacity: 0; /* Start hidden */
  }

  /* Styling for line-clamp if not globally available (Tailwind might have a plugin for this) */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;  
    overflow: hidden;
  }

  /* 
  Optional: CSS animations for internal elements if `internal-elements-visible` class is added by JS. 
  These would replace or augment the previous JS-driven direct style manipulations.
  Example:
  .facility-card .card-icon {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.5s ease 0.1s, transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s;
  }
  .facility-card.internal-elements-visible .card-icon {
    opacity: 1;
    transform: scale(1);
  }
  
  .facility-card .card-title {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.5s ease 0.2s, transform 0.5s ease 0.2s, color 0.3s ease;
  }
  .facility-card.internal-elements-visible .card-title {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* ... and so on for other elements like description, features, button ... */
  */
</style>
