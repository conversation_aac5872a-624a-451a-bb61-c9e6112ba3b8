---
// Define the props for this component
interface TechStat {
  value: string;
  label: string;
  icon: string;
}

interface Props {
  stats: TechStat[];
}

const { stats } = Astro.props;

// Function to get icon SVG based on name
const getIconSvg = (iconName: string) => {
  // Using a slightly more compact style for icons within stat cards
  const attrs = `width="20" height="20" stroke-width="1.5"`; 
  switch(iconName) {
    case 'wifi':
      return `<svg xmlns="http://www.w3.org/2000/svg" ${attrs} viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="M5 13a10 10 0 0 1 14 0"/><path d="M8.5 16.5a5 5 0 0 1 7 0"/><path d="M2 8.82a15 15 0 0 1 20 0"/><line x1="12" x2="12.01" y1="20" y2="20"/></svg>`;
    case 'clock':
      return `<svg xmlns="http://www.w3.org/2000/svg" ${attrs} viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><polyline points="12 6 12 12 16 14"/></svg>`;
    case 'monitor':
      return `<svg xmlns="http://www.w3.org/2000/svg" ${attrs} viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="14" x="2" y="3" rx="2"/><line x1="8" x2="16" y1="21" y2="21"/><line x1="12" x2="12" y1="17" y2="21"/></svg>`;
    case 'zap':
      return `<svg xmlns="http://www.w3.org/2000/svg" ${attrs} viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"/></svg>`;
    default:
      return `<svg xmlns="http://www.w3.org/2000/svg" ${attrs} viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle></svg>`;
  }
};
---

<div class="tech-section py-16 md:py-24">
  <div class="container mx-auto">
    <!-- Section Header -->
    <div class="max-w-3xl mx-auto text-center mb-12 md:mb-16">
      <div class="animate-slide-up" style="--slide-delay: 0ms;">
        <h2 class="text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl fancy-heading">
          <span class="gradient-text">Cutting-Edge</span> Technology
        </h2>
      </div>
      <div class="animate-slide-up" style="--slide-delay: 200ms;">
        <p class="mt-4 text-lg text-muted-foreground">
          Our campus is equipped with high-speed Wi-Fi, smart classrooms, and digital learning tools to ensure students have access to industry-standard equipment and software.
        </p>
      </div>
    </div>

    <!-- Tech stats cards -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
      {stats.map((stat, index) => (
        <div class="animate-slide-up tech-stat-card" style={`--slide-delay: ${(index * 100) + 300}ms;`}>
          <div class="relative overflow-hidden rounded-xl border border-border/40 bg-background/70 p-6 backdrop-blur-md transition-all duration-300 hover:-translate-y-1.5 hover:shadow-xl hover:shadow-primary/10 hover:border-primary/30 h-full flex flex-col items-center text-center">
            <div class="absolute -right-5 -top-5 h-16 w-16 rounded-full bg-primary/10 opacity-70 group-hover:opacity-100 transition-opacity"></div>
            <div class="relative z-10 flex flex-col items-center">
              <div class="mb-3 rounded-full bg-primary/10 p-3 text-primary tech-icon">
                <Fragment set:html={getIconSvg(stat.icon)} />
              </div>
              <div class="text-3xl font-bold count-up tech-value" data-target-value={stat.value}>{stat.value.match(/^\d+/)?.[0] || stat.value}</div>
              <div class="text-sm text-muted-foreground mt-1 tech-label">{stat.label}</div>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const countUpElements = document.querySelectorAll('.count-up');

    countUpElements.forEach(element => {
      const rawTargetValue = element.getAttribute('data-target-value');
      if (!rawTargetValue) return;

      const numericPart = parseInt(rawTargetValue.replace(/[^0-9]/g, ''), 10);
      const suffix = rawTargetValue.replace(/\d|\s/g, ''); // Extracts non-digits like %, +, Gbps

      if (isNaN(numericPart)) {
        element.textContent = rawTargetValue; // If no number, just set text
        return;
      }

      let currentCount = 0;
      const duration = 1500; // ms
      const frameDuration = 1000 / 60; // 60fps
      const totalFrames = Math.round(duration / frameDuration);
      const countIncrement = numericPart / totalFrames;

      // Check if element is visible before starting animation
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const counter = setInterval(() => {
              currentCount += countIncrement;
              if (currentCount >= numericPart) {
                element.textContent = numericPart + suffix;
                clearInterval(counter);
              } else {
                element.textContent = Math.floor(currentCount) + suffix;
              }
            }, frameDuration);
            observer.unobserve(element); // Stop observing after animation starts
          }
        });
      }, { threshold: 0.1 });

      observer.observe(element);
    });
  });
</script>

<style>
  /* Ensure slide-up animation is available (globally or defined here if not) */
  @keyframes slide-up {
    0% { transform: translateY(30px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }
  .animate-slide-up {
    animation: slide-up 0.8s ease forwards;
    animation-delay: var(--slide-delay, 0s);
    opacity: 0; /* Start hidden */
  }

  /* Gradient text (if not global) */
  .gradient-text {
    background: linear-gradient(to right, hsl(var(--primary)), hsl(var(--primary) / 0.8));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
  }

  /* Fancy heading (if not global) */
  .fancy-heading {
    position: relative;
  }
  .fancy-heading::after {
    content: '';
    position: absolute;
    bottom: -0.1em;
    left: 50%;
    transform: translateX(-50%);
    width: 40%;
    height: 2px;
    background: hsl(var(--primary) / 0.5);
    transition: width 0.3s ease;
  }
  .fancy-heading:hover::after {
    width: 60%;
  }

  /* Minimal styling for card elements if needed, Tailwind should cover most */
  .tech-stat-card .tech-icon svg {
    width: 28px; /* Slightly larger icon in the circle */
    height: 28px;
  }
</style>
