---
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
---

<section class="relative w-full overflow-hidden">
  <!-- Full-screen background image with overlay -->
  <div class="absolute inset-0 z-0">
    <img 
      src="/campus.png" 
      alt="DCCP Campus" 
      class="h-full w-full object-cover"
    />
    <div class="absolute inset-0 bg-gradient-to-r from-background/95 via-background/80 to-background/30 dark:from-background/98 dark:via-background/90 dark:to-background/70"></div>
  </div>

  <!-- Content container -->
  <div class="container relative z-10 mx-auto px-4 py-24 sm:px-6 lg:flex lg:h-screen lg:items-center lg:px-8">
    <div class="flex flex-col items-start space-y-8 lg:w-3/5">
      <!-- Animated badge -->
      <div class="animate-fade-in-up" style="--animation-delay: 0.2s;">
        <Badge variant="outline" className="border-primary/20 bg-primary/10 backdrop-blur-sm px-4 py-1.5 text-sm">
          <span class="mr-1 inline-block h-2 w-2 animate-pulse rounded-full bg-primary"></span>
          Now Accepting Applications
        </Badge>
      </div>
      
      <!-- Main heading with animated reveal -->
      <div class="space-y-4">
        <h1 class="animate-reveal-text text-4xl font-extrabold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
          <span class="block">Shape Your Future at</span>
          <span class="block text-primary">Data Center College</span>
          <span class="block text-xl font-medium md:text-2xl">Baguio City, Philippines</span>
        </h1>
        <p class="animate-fade-in-up max-w-xl text-muted-foreground md:text-xl" style="--animation-delay: 0.4s;">
          A premier institution for technology and business education, empowering students with the skills and knowledge needed for tomorrow's challenges.
        </p>
      </div>
      
      <!-- CTA buttons with staggered animation -->
      <div class="flex flex-wrap gap-4 animate-fade-in-up" style="--animation-delay: 0.6s;">
        <Button size="lg" className="group relative overflow-hidden">
          <span class="relative z-10">Explore Programs</span>
          <span class="absolute bottom-0 left-0 h-1 w-0 bg-primary-foreground transition-all duration-300 group-hover:w-full"></span>
        </Button>
        <Button variant="outline" size="lg" className="group">
          <span>Apply Now</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1">
            <path d="M5 12h14"></path>
            <path d="m12 5 7 7-7 7"></path>
          </svg>
        </Button>
      </div>
      
      <!-- Scrolling features ticker -->
      <div class="mt-8 w-full max-w-2xl overflow-hidden border-t border-b border-border py-4 animate-fade-in-up" style="--animation-delay: 0.8s;">
        <div class="animate-ticker flex gap-4 whitespace-nowrap">
          <div class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-primary">
              <path d="M22 10v6M2 10l10-5 10 5-10 5z"/>
              <path d="M6 12v5c3 3 9 3 12 0v-5"/>
            </svg>
            <span>Cutting-edge Technology Programs</span>
          </div>
          <div class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-primary">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
              <circle cx="9" cy="7" r="4"/>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
            </svg>
            <span>Expert Faculty</span>
          </div>
          <div class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-primary">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
            </svg>
            <span>95% Employment Rate</span>
          </div>
          <div class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-primary">
              <path d="M3 3v18h18"/>
              <path d="m19 9-5 5-4-4-3 3"/>
            </svg>
            <span>Industry-Aligned Curriculum</span>
          </div>
          <div class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-primary">
              <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/>
              <circle cx="12" cy="10" r="3"/>
            </svg>
            <span>Prime Location in Baguio City</span>
          </div>
        </div>
      </div>
      
      <!-- Scroll indicator -->
      <div class="absolute bottom-8 left-1/2 hidden -translate-x-1/2 animate-bounce md:block">
        <a href="#about" class="flex flex-col items-center text-muted-foreground transition-colors hover:text-foreground">
          <span class="text-xs font-medium">Discover More</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mt-2 h-5 w-5">
            <path d="m6 9 6 6 6-6"/>
          </svg>
        </a>
      </div>
    </div>
  </div>
  
  <!-- Decorative elements -->
  <div class="pointer-events-none absolute inset-0 z-20 overflow-hidden">
    <!-- Top-right decorative element -->
    <div class="absolute -right-20 -top-20 h-[300px] w-[300px] rounded-full border border-primary/20 bg-primary/5 backdrop-blur-3xl"></div>
    
    <!-- Bottom-left decorative element -->
    <div class="absolute -bottom-32 -left-32 h-[400px] w-[400px] rounded-full border border-primary/10 bg-primary/5 backdrop-blur-3xl"></div>
  </div>
</section>

<style>
  /* Animation keyframes */
  @keyframes reveal-text {
    0% {
      transform: translateY(20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  @keyframes fade-in-up {
    0% {
      transform: translateY(20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  @keyframes ticker {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-100%);
    }
  }
  
  /* Apply animations */
  .animate-reveal-text {
    animation: reveal-text 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    opacity: 0;
  }
  
  .animate-fade-in-up {
    animation: fade-in-up 0.8s ease forwards;
    animation-delay: var(--animation-delay, 0s);
    opacity: 0;
  }
  
  .animate-ticker {
    animation: ticker 20s linear infinite;
  }
  
  .animate-ticker:hover {
    animation-play-state: paused;
  }
  
  /* Ensure the ticker content is duplicated for seamless looping */
  .animate-ticker {
    display: flex;
  }
  
  .animate-ticker > * {
    flex-shrink: 0;
    margin-right: 2rem;
  }
  
  .animate-ticker > *:last-child {
    margin-right: 0;
  }
  
  /* Ensure the ticker content is duplicated for seamless looping */
  .animate-ticker {
    display: flex;
  }
  
  .animate-ticker > * {
    flex-shrink: 0;
    padding-right: 2rem;
  }
</style>
