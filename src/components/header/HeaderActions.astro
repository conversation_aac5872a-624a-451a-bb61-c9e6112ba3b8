---
import ThemeToggle from "@/components/ThemeToggle.astro";
---

<div class="flex items-center space-x-2">
  <!-- Search Button -->
  <button id="search-button" class="p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent/50 transition-colors duration-300 group">
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform duration-300 group-hover:scale-110"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>
    <span class="sr-only">Search</span>
  </button>

  <!-- Apply Now Button -->
  <a
    href="#admissions"
    class="apply-button relative overflow-hidden group px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-300 text-sm font-medium"
  >
    <span class="relative z-10">Apply Now</span>
    <span class="absolute inset-0 bg-primary-foreground/10 scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></span>
  </a>

  <!-- Theme Toggle -->
  <ThemeToggle />
</div>
