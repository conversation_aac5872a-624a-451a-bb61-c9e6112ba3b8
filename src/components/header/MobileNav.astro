---
import ThemeToggle from "@/components/ThemeToggle.astro";
---

<!-- Mobile Menu Button -->
<div class="lg:hidden flex items-center gap-2">
  <button id="search-button-mobile" class="p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent/50 transition-colors duration-300 group">
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 transition-transform duration-300 group-hover:scale-110"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>
    <span class="sr-only">Search</span>
  </button>

  <button id="mobile-menu-button" class="p-2 rounded-md hover:bg-accent/50 transition-colors duration-300 group">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 transition-transform duration-300 group-hover:scale-110"><line x1="4" x2="20" y1="12" y2="12"/><line x1="4" x2="20" y1="6" y2="6"/><line x1="4" x2="20" y1="18" y2="18"/></svg>
    <span class="sr-only">Menu</span>
  </button>
</div>

<!-- Mobile Menu (hidden by default) -->
<div id="mobile-menu" class="fixed inset-0 z-40 lg:hidden hidden">
  <div class="fixed inset-0 bg-background/80 backdrop-blur-sm" id="mobile-backdrop"></div>

  <div class="fixed inset-y-0 right-0 w-full max-w-sm bg-background border-l shadow-xl transition-transform duration-300 translate-x-full" id="mobile-container">
    <div class="flex flex-col h-full overflow-y-auto">
      <!-- Mobile Menu Header -->
      <div class="sticky top-0 z-10 flex items-center justify-between px-4 py-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div class="flex items-center gap-2">
          <div class="rounded-full bg-primary/10 p-1.5">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-primary"><path d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c3 3 9 3 12 0v-5"/></svg>
          </div>
          <span class="text-sm font-medium">DCCP Baguio</span>
        </div>
        <button id="close-mobile-menu" class="p-2 rounded-md hover:bg-accent/50 transition-colors duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
          <span class="sr-only">Close menu</span>
        </button>
      </div>

      <!-- Mobile Menu Content -->
      <div class="flex-1 px-4 py-6 space-y-6">
        <!-- Main Navigation Links -->
        <nav class="space-y-1.5">
          <a href="#about" class="mobile-nav-link flex items-center gap-2 px-3 py-2.5 rounded-md hover:bg-accent/50 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-primary"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
            <span>About</span>
          </a>

          <!-- Programs Collapsible -->
          <div class="mobile-collapsible">
            <button class="mobile-collapsible-trigger flex items-center justify-between w-full px-3 py-2.5 rounded-md hover:bg-accent/50 transition-colors">
              <div class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-primary"><path d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c3 3 9 3 12 0v-5"/></svg>
                <span>Programs</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 transition-transform duration-200"><polyline points="6 9 12 15 18 9"></polyline></svg>
            </button>
            <div class="mobile-collapsible-content hidden pl-9 pr-3 pb-1 pt-1 space-y-1">
              <a href="#undergraduate" class="block px-3 py-2 text-sm rounded-md hover:bg-accent/50 transition-colors">
                Undergraduate Programs
              </a>
              <a href="#graduate" class="block px-3 py-2 text-sm rounded-md hover:bg-accent/50 transition-colors">
                Graduate Programs
              </a>
              <a href="#certificate" class="block px-3 py-2 text-sm rounded-md hover:bg-accent/50 transition-colors">
                Certificate Courses
              </a>
              <a href="#online" class="block px-3 py-2 text-sm rounded-md hover:bg-accent/50 transition-colors">
                Online Learning
              </a>
            </div>
          </div>

          <!-- Facilities Collapsible -->
          <div class="mobile-collapsible">
            <button class="mobile-collapsible-trigger flex items-center justify-between w-full px-3 py-2.5 rounded-md hover:bg-accent/50 transition-colors">
              <div class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-primary"><path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"/><path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9"/><path d="M12 3v6"/></svg>
                <span>Facilities</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 transition-transform duration-200"><polyline points="6 9 12 15 18 9"></polyline></svg>
            </button>
            <div class="mobile-collapsible-content hidden pl-9 pr-3 pb-1 pt-1 space-y-1">
              <a href="#library" class="block px-3 py-2 text-sm rounded-md hover:bg-accent/50 transition-colors">
                Library & Learning Center
              </a>
              <a href="#labs" class="block px-3 py-2 text-sm rounded-md hover:bg-accent/50 transition-colors">
                Laboratories
              </a>
              <a href="#sports" class="block px-3 py-2 text-sm rounded-md hover:bg-accent/50 transition-colors">
                Sports Facilities
              </a>
              <a href="#campus" class="block px-3 py-2 text-sm rounded-md hover:bg-accent/50 transition-colors">
                Campus Tour
              </a>
            </div>
          </div>

          <a href="#testimonials" class="mobile-nav-link flex items-center gap-2 px-3 py-2.5 rounded-md hover:bg-accent/50 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-primary"><path d="M17 6.1H3"/><path d="M21 12.1H3"/><path d="M15.1 18H3"/></svg>
            <span>Testimonials</span>
          </a>

          <a href="#admissions" class="mobile-nav-link flex items-center gap-2 px-3 py-2.5 rounded-md hover:bg-accent/50 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-primary"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>
            <span>Admissions</span>
          </a>

          <a href="#contact" class="mobile-nav-link flex items-center gap-2 px-3 py-2.5 rounded-md hover:bg-accent/50 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-primary"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/></svg>
            <span>Contact</span>
          </a>

          <a href="/dccp-hub" class="mobile-nav-link flex items-center gap-2 px-3 py-2.5 rounded-md hover:bg-accent/50 transition-colors bg-primary/5">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-primary"><path d="M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6"></path><path d="m21 3-9 9"></path><path d="M15 3h6v6"></path></svg>
            <div class="flex items-center gap-1">
              <span>DCCPHub</span>
              <div class="h-1.5 w-1.5 rounded-full bg-primary animate-pulse"></div>
            </div>
          </a>
        </nav>

        <!-- Mobile Action Buttons -->
        <div class="space-y-3 pt-4 border-t">
          <a href="#admissions" class="flex items-center justify-center gap-2 w-full py-2.5 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
            <span>Apply Now</span>
          </a>

          <div class="flex items-center justify-between px-3 py-2 rounded-md bg-accent/50">
            <span class="text-sm">Switch Theme</span>
            <ThemeToggle />
          </div>
        </div>
      </div>

      <!-- Mobile Menu Footer -->
      <div class="border-t px-4 py-4">
        <div class="flex items-center justify-between">
          <div class="text-xs text-muted-foreground">
            <p>© 2023 DCCP Baguio</p>
          </div>
          <div class="flex items-center space-x-3">
            <a href="#" class="text-muted-foreground hover:text-foreground transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"/></svg>
              <span class="sr-only">Facebook</span>
            </a>
            <a href="#" class="text-muted-foreground hover:text-foreground transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4"><rect width="20" height="20" x="2" y="2" rx="5" ry="5"/><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"/><line x1="17.5" x2="17.51" y1="6.5" y2="6.5"/></svg>
              <span class="sr-only">Instagram</span>
            </a>
            <a href="#" class="text-muted-foreground hover:text-foreground transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"/></svg>
              <span class="sr-only">Twitter</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
