---
---

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Elements
    const header = document.getElementById('college-header');
    const navLinks = document.querySelectorAll('.nav-link');
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const closeMobileMenuButton = document.getElementById('close-mobile-menu');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileContainer = document.getElementById('mobile-container');
    const mobileBackdrop = document.getElementById('mobile-backdrop');
    const mobileCollapsibles = document.querySelectorAll('.mobile-collapsible');
    const searchButton = document.getElementById('search-button');
    const searchButtonMobile = document.getElementById('search-button-mobile');
    const searchOverlay = document.getElementById('search-overlay');
    const searchContainer = document.getElementById('search-container');
    const closeSearchButton = document.getElementById('close-search');

    // Scroll effect for header
    let lastScrollY = window.scrollY;

    window.addEventListener('scroll', () => {
      if (window.scrollY > 50) {
        header.classList.add('shadow-sm');
        header.classList.add('h-16');
        header.classList.remove('h-20');
      } else {
        header.classList.remove('shadow-sm');
        header.classList.remove('h-16');
        header.classList.add('h-20');
      }

      // Show/hide header on scroll direction
      if (window.scrollY > lastScrollY && window.scrollY > 150) {
        header.style.transform = 'translateY(-100%)';
      } else {
        header.style.transform = 'translateY(0)';
      }
      lastScrollY = window.scrollY;
    });

    // Desktop nav link hover effect
    navLinks.forEach(link => {
      link.addEventListener('mouseenter', () => {
        const indicator = link.querySelector('.nav-indicator');
        if (indicator) {
          indicator.style.transform = 'scaleX(1)';
        }
      });

      link.addEventListener('mouseleave', () => {
        const indicator = link.querySelector('.nav-indicator');
        if (indicator) {
          indicator.style.transform = 'scaleX(0)';
        }
      });
    });

    // Mobile menu functionality
    function openMobileMenu() {
      if (mobileMenu && mobileContainer) {
        document.body.style.overflow = 'hidden';
        mobileMenu.classList.remove('hidden');
        // Trigger animation after a small delay
        setTimeout(() => {
          mobileContainer.style.transform = 'translateX(0)';
          mobileBackdrop.style.opacity = '1';
        }, 10);
      }
    }

    function closeMobileMenu() {
      if (mobileMenu && mobileContainer) {
        mobileContainer.style.transform = 'translateX(100%)';
        mobileBackdrop.style.opacity = '0';
        // Wait for animation to complete before hiding
        setTimeout(() => {
          mobileMenu.classList.add('hidden');
          document.body.style.overflow = '';
        }, 300);
      }
    }

    if (mobileMenuButton) {
      mobileMenuButton.addEventListener('click', openMobileMenu);
    }

    if (closeMobileMenuButton) {
      closeMobileMenuButton.addEventListener('click', closeMobileMenu);
    }

    if (mobileBackdrop) {
      mobileBackdrop.addEventListener('click', closeMobileMenu);
    }

    // Mobile collapsible functionality
    mobileCollapsibles.forEach(collapsible => {
      const trigger = collapsible.querySelector('.mobile-collapsible-trigger');
      const content = collapsible.querySelector('.mobile-collapsible-content');
      const icon = trigger.querySelector('svg:last-child');

      trigger.addEventListener('click', () => {
        content.classList.toggle('hidden');
        if (!content.classList.contains('hidden')) {
          icon.style.transform = 'rotate(180deg)';
        } else {
          icon.style.transform = 'rotate(0)';
        }
      });
    });

    // Search functionality
    function openSearch() {
      if (searchOverlay && searchContainer) {
        document.body.style.overflow = 'hidden';
        searchOverlay.classList.remove('hidden');
        // Trigger animation after a small delay
        setTimeout(() => {
          searchContainer.style.opacity = '1';
          searchContainer.style.transform = 'scale(1)';
        }, 10);
      }
    }

    function closeSearch() {
      if (searchOverlay && searchContainer) {
        searchContainer.style.opacity = '0';
        searchContainer.style.transform = 'scale(0.95)';
        // Wait for animation to complete before hiding
        setTimeout(() => {
          searchOverlay.classList.add('hidden');
          document.body.style.overflow = '';
        }, 300);
      }
    }

    if (searchButton) {
      searchButton.addEventListener('click', openSearch);
    }

    if (searchButtonMobile) {
      searchButtonMobile.addEventListener('click', openSearch);
    }

    if (closeSearchButton) {
      closeSearchButton.addEventListener('click', closeSearch);
    }

    if (searchOverlay) {
      searchOverlay.addEventListener('click', (e) => {
        if (e.target === searchOverlay) {
          closeSearch();
        }
      });
    }

    // Close mobile menu when clicking on a link
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link, .mobile-collapsible-content a');
    mobileNavLinks.forEach(link => {
      link.addEventListener('click', () => {
        closeMobileMenu();
      });
    });
  });
</script>
