{"name": "@area44/astro-shadcn-ui-template", "version": "25.01.27", "description": "An Astro template for building apps.", "author": {"name": "AREA44"}, "license": "MIT", "scripts": {"build": "astro build", "dev": "astro dev --host", "preview": "astro preview", "start": "astro dev --host", "format": "biome format --write ."}, "dependencies": {"@astrojs/react": "^4.3.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "astro": "^5.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.488.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-resizable-panels": "^2.1.9", "recharts": "^2.15.4", "sharp": "^0.34.2", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.76"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^22.16.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.8.0"}