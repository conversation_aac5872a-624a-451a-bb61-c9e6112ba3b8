{"formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto", "ignore": ["**/*.min.js", "**/*.min.css", "**/dist/", "**/node_modules/", "**/pnpm-lock.yaml", "**/.netlify/", "**/.astro/", "**/.github/"]}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true}}, "javascript": {"globals": ["Astro"], "formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto"}}}