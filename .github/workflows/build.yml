# To get started with <PERSON><PERSON> see: https://docs.astro.build/en/getting-started
name: Build with <PERSON><PERSON>

on:
  pull_request:
    branches: ['main']

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout your repository using git
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v2

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: pnpm
          cache-dependency-path: pnpm-lock.yaml

      - name: Install dependencies
        run: pnpm install

      - name: Build with Astro
        run: pnpm build
