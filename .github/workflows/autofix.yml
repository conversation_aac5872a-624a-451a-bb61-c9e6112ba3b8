name: autofix.ci

on:
  pull_request:
  push:
    branches: ['main']
permissions:
  contents: read

jobs:
  autofix:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout your repository using git
        uses: actions/checkout@v4

      - name: Install PNPM
        uses: pnpm/action-setup@v4

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Setup Biome CLI
        uses: biomejs/setup-biome@v2
        with:
          version: latest

      - name: Run Biome
        run: biome format --write .

      - name: Run autofix.ci
        uses: autofix-ci/action@v1.3
